<?php

return [

    /**
     * Official list of interface text (in other words: language strings) that can be overridden.
     */
    'overridable_keys' => [

        'Entries' => [
            'buttons.add_more_entries',
            'entries.actions.view_entrant_pdf.label',
            'entries.form.add-contributor',
            'entries.form.add-link',
            'entries.form.incomplete.multi-chapter',
            'entries.form.incomplete.single-chapter',
            'entries.form.referee.send-request',
            'entries.form.resubmit',
            'entries.form.submit',
            'entries.form.check_eligibility',
            'entries.form.title.help',
            'entries.form.title.label',
            'entries.messages.closed',
            'entries.messages.max-attachments',
            'entries.messages.max-entries',
            'entries.messages.min-attachments',
            'entries.messages.submitted',
            'entries.moderation.status.approved',
            'entries.moderation.status.not-rejected',
            'entries.moderation.status.rejected',
            'entries.moderation.status.undecided',
            'entries.no_entries.button',
            'entries.no_entries.message',
            'entries.recuse.actions.all',
            'entries.status.resubmission_required',
            'entries.status.resubmitted',
            'entries.table.columns.title',
            'entries.table.empty',
            'entries.titles.entrant',
            'entries.titles.manager',
            'entries.titles.qualify',
            'entries.titles.start',
            'entries.titles.top-pick',
            'fields.messages.required',
            'files.buttons.attachments',
            'review-flow.tasks.title',
            'review-flow.titles.manage',
            'rounds.open_rounds.label',
            'seasons.selector.active',
            'entries.form.blank_pdf_download',
            'pdf.upper_header.title',
            'pdf.upper_header.subtitle',
            'entries.form.link-extra.label',
        ],

        'Content blocks' => [
            'content-block.menu.links.about',
        ],

        'Categories' => [
            'category.titles.new',
            'category.table.has-children',
            'setup-guide.steps.categories.heading',
        ],

        'Fields' => [
            'fields.form.categories_all.label',
            'fields.form.categories_select.label',
        ],

        'Payments' => [
            'buttons.add-entries',
            'buttons.pay-enter',
            'ecommerce.cart.empty',
            'ecommerce.cart.form.member_number.label',
            'ecommerce.cart.info.payment_received',
            'ecommerce.cart.table.member',
            'orders.table.columns.member_number',
            'payments.form.tax_number.label',
            'payments.prices.member_number',
            'payments.invoice.pdf.totals.thanks',
            'ecommerce.cart.info.ready_for_payment',
            'ecommerce.cart.titles.entry-fee',
            'ecommerce.cart.form.company-name.label',
        ],

        'ReviewFlow' => [
            'review-flow.tasks.entry',
        ],

        'Home' => [
            'buttons.register',
            'home.login.email.label',
            'home.login.email.help',
            'home.login.login_or_register',
            'home.login.saml',
            'home.register.heading',
            'home.register.user_exists_body',
            'home.register.password.label',
            'home.register.password_confirmation.label',
        ],

        'Judging' => [
            'entries.titles.judge',
            'judging.form.comment.heading',
            'judging.form.abstain.label',
            'judging.index.judging_progress.picks_per_category.label',
            'judging.titles.by_category',
            'judging.titles.leaderboard',
            'judging.titles.main',
            'judging.titles.progress',
            'panels.form.mode.label',
            'panels.search.labels.judging_mode',
            'panels.table.columns.mode',
            'qualifying.titles.main',
            'score-set.form.mode.label',
            'score-set.form.top-pick-mode.all',
            'score-set.form.top-pick-mode.categories',
            'score-set.form.category-quick-filter.label',
            'score-set.form.score_display.label',
            'score-set.table.columns.mode',
            'score-set.titles.entry-list',
            'voting.controls.vote',
            'voting.controls.voted',
            'judging.table.columns.badge',
            'judging.table.columns.category',
            'judging.picks.top',
            'judging.picks.bottom',
        ],

        'Miscellaneous' => [
            'miscellaneous.alerts.validator.message',
            'miscellaneous.alerts.validator.short_message',
            'integrations.form.crm.payment.fields.memberNumber',
            'integrations.validation.crm.settings.payment.fields.memberNumber.required_with',
        ],

        'Validation' => [
            'validation.phone_format',
        ],

        'Auth' => [
            'auth.request_login_link.email_sent',
            'auth.request_login_link.notify_change_password',
            'auth.request_login_code.code-sent',
            'auth.request_login_code.code-resent',
        ],

        'Grant reports' => [
            'grant_reports.messages.closed',
        ],
    ],

    'depends-on-features' => [
        'Grant reports' => 'grant_reports',
    ],

    'excluded_overridable_keys_for_tooltip' => [
        'auth.request_login_link.notify_change_password',
    ],
];
