<?php

return [
    // Available triggers and their supported merge fields
    'triggers' => [
        'entry_moderated' => [
            'account_name',
            'first_name',
            'last_name',
            'entry_name',
            'entry_slug',
            'entry_local_id',
            'parent_category',
            'category',
            'chapter',
            'account_url',
            'fund_allocations',
            'entry_field:abcd1234',
            'user_field:abcd1234',
        ],

        'entry_submitted' => [
            'account_name',
            'first_name',
            'last_name',
            'entry_name',
            'entry_slug',
            'entry_local_id',
            'parent_category',
            'category',
            'chapter',
            'account_url',
            'fund_allocations',
            'entry_field:abcd1234',
            'user_field:abcd1234',
        ],

        'entry_tagged' => [
            'account_name',
            'first_name',
            'last_name',
            'entry_name',
            'entry_slug',
            'entry_local_id',
            'parent_category',
            'category',
            'chapter',
            'account_url',
            'fund_allocations',
            'entry_field:abcd1234',
            'user_field:abcd1234',
        ],

        'user_registered' => [
            'account_name',
            'first_name',
            'last_name',
            'account_url',
            'user_field:abcd1234',
        ],

        'user_invited' => [
            'account_name',
            'message',
            'join_link',
        ],

        'role_granted' => [
            'account_name',
            'first_name',
            'last_name',
            'account_url',
            'user_field:abcd1234',
        ],

        'assignment_created' => [
            'assignment_url',
            'account_name',
            'first_name',
            'last_name',
            'entry_name',
            'entry_slug',
            'entry_local_id',
            'entrant_email',
            'parent_category',
            'category',
            'chapter',
            'account_url',
            'entry_field:abcd1234',
        ],

        'assignment_judging_completed' => [
            'account_name',
            'first_name',
            'last_name',
            'entry_name',
            'entry_slug',
            'entry_local_id',
            'parent_category',
            'category',
            'chapter',
            'account_url',
            'fund_allocations',
            'entry_field:abcd1234',
            'user_field:abcd1234',
        ],

        'entry_invited' => [
            'account_name',
            'message',
            'invite_link',
        ],

        'document_created' => [
            'account_name',
            'account_url',
            'date_document_created',
        ],

        //'user_confirmed' => [],

        //'password_reset' => [],
    ],

    'feature_triggers' => [
        'grants' => [
            'grant_status_changed' => [
                'account_name',
                'first_name',
                'last_name',
                'entry_name',
                'entry_slug',
                'entry_local_id',
                'parent_category',
                'category',
                'chapter',
                'account_url',
                'fund_allocations',
                'entry_field:abcd1234',
                'user_field:abcd1234',
            ],
        ],

        'grant_reports' => [
            'grant_report_submitted' => [
                'account_name',
                'first_name',
                'last_name',
                'report_name',
                'report_due',
                'report_url',
                'report_slug',
                'entry_name',
                'entry_slug',
                'entry_local_id',
                'parent_category',
                'category',
                'chapter',
                'account_url',
                'entry_field:abcd1234',
                'user_field:abcd1234',
                'report_field:abcd1234',
            ],

            'grant_report_scheduled' => [
                'account_name',
                'first_name',
                'last_name',
                'report_name',
                'report_due',
                'report_url',
                'report_slug',
                'entry_name',
                'entry_slug',
                'entry_local_id',
                'parent_category',
                'category',
                'chapter',
                'account_url',
                'entry_field:abcd1234',
                'user_field:abcd1234',
                'report_field:abcd1234',
            ],

            'grant_report' => [
                'account_name',
                'first_name',
                'last_name',
                'report_name',
                'report_due',
                'report_url',
                'report_slug',
                'entry_name',
                'entry_slug',
                'entry_local_id',
                'parent_category',
                'category',
                'chapter',
                'account_url',
                'entry_field:abcd1234',
                'user_field:abcd1234',
            ],
        ],

        'review_flow' => [
            'review_stage_started' => [
                'review_url',
                'account_name',
                'first_name',
                'last_name',
                'entry_name',
                'entry_slug',
                'entry_local_id',
                'entrant_name',
                'entrant_email',
                'parent_category',
                'category',
                'chapter',
                'account_url',
                'fund_allocations',
                'entry_field:abcd1234',
            ],

            'review_stage_completed' => [
                'review_url',
                'account_name',
                'first_name',
                'last_name',
                'entry_name',
                'entry_slug',
                'entry_local_id',
                'entrant_name',
                'entrant_email',
                'parent_category',
                'category',
                'chapter',
                'account_url',
                'fund_allocations',
                'entry_field:abcd1234',
            ],
        ],

        'eligibility' => [
            'entry_eligible' => [
                'account_name',
                'first_name',
                'last_name',
                'entry_name',
                'entry_slug',
                'entry_local_id',
                'parent_category',
                'category',
                'chapter',
                'account_url',
                'entry_field:abcd1234',
                'user_field:abcd1234',
            ],

            'entry_ineligible' => [
                'account_name',
                'first_name',
                'last_name',
                'entry_name',
                'entry_slug',
                'entry_local_id',
                'parent_category',
                'category',
                'chapter',
                'account_url',
                'entry_field:abcd1234',
                'user_field:abcd1234',
            ],
        ],

        'resubmission' => [
            'entry_resubmitted' => [
                'account_name',
                'first_name',
                'last_name',
                'entry_name',
                'entry_slug',
                'entry_local_id',
                'category',
                'chapter',
                'account_url',
                'fund_allocations',
                'entry_field:abcd1234',
                'user_field:abcd1234',
            ],

            'resubmission_required' => [
                'account_name',
                'first_name',
                'last_name',
                'entry_name',
                'entry_slug',
                'entry_local_id',
                'parent_category',
                'category',
                'chapter',
                'account_url',
                'fund_allocations',
                'entry_field:abcd1234',
                'user_field:abcd1234',
            ],
        ],

        'fund_management' => [
            'payment_due_date' => [
                'account_name',
                'first_name',
                'last_name',
                'entry_name',
                'entry_slug',
                'entry_local_id',
                'parent_category',
                'category',
                'chapter',
                'account_url',
                'fund_allocations',
                'entry_field:abcd1234',
                'user_field:abcd1234',
                'payment_method',
                'payment_date_due',
                'payment_reference',
                'payment_currency',
                'payment_amount',
                'payment_status',
            ],

            'payment_status_changed' => [
                'account_name',
                'first_name',
                'last_name',
                'entry_name',
                'entry_slug',
                'entry_local_id',
                'parent_category',
                'category',
                'chapter',
                'account_url',
                'fund_allocations',
                'entry_field:abcd1234',
                'user_field:abcd1234',
                'payment_method',
                'payment_date_due',
                'payment_reference',
                'payment_currency',
                'payment_amount',
                'payment_status',
            ],
        ],

        'order_payments' => [
            'payment_pending' => [
                'account_name',
                'first_name',
                'last_name',
                'payment_amount',
                'order_id',
                'invoice_url',
                'account_url',
                'user_field:abcd1234',
            ],

            'payment_success' => [
                'account_name',
                'first_name',
                'last_name',
                'payment_amount',
                'order_id',
                'invoice_url',
                'account_url',
                'user_field:abcd1234',
            ],
        ],

        'collaboration' => [
            'collaborator_invited' => [
                'account_name',
                'message',
                'invite_link',
            ],
        ],
    ],

    'seed' => [
        'collaborator_invited',
        'entry_submitted',
        'user_invited',
        'payment_pending',
        'payment_success',
        'entry_eligible',
        'entry_ineligible',
        'grant_report',
        'entry_invited',
    ],

    'reseed_triggers' => [
        'user_invited',
        'collaborator_invited',
    ],

    'triggers_with_inner_markdown' => [
        'collaborator_invited' => [
            'message',
        ],
        'user_invited' => [
            'message',
        ],
        'entry_invited' => [
            'message',
        ],
    ],

    'triggers_with_inner_style' => [
        'fund_allocations' => 'emails.templates.fund-allocations-table-style',
    ],

    'sms' => [
        'max_characters' => 160,
    ],

    'review_stage_allowed_triggers' => [
        'completed' => [
            'review.stage.completed',
        ],
        'started' => [
            'review.stage.started',
        ],
    ],
];
