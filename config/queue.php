<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Queue Driver
    |--------------------------------------------------------------------------
    |
    | Laravel's queue API supports an assortment of back-ends via a single
    | API, giving you convenient access to each back-end using the same
    | syntax for each one. Here you may set the default queue driver.
    |
    | Supported: "sync", "database", "beanstalkd", "sqs", "redis", "null"
    |
    */

    'default' => env('QUEUE_DRIVER', 'sync'),

    /*
    |--------------------------------------------------------------------------
    | Queue Connections
    |--------------------------------------------------------------------------
    |
    | Here you may configure the connection information for each server that
    | is used by your application. A default configuration has been added
    | for each back-end shipped with Laravel. You are free to add more.
    |
    */

    'connections' => array_merge([

        'sync' => [
            'driver' => 'sync',
        ],

        'database' => [
            'driver' => 'database',
            'table' => 'jobs',
            'queue' => 'default',
            'retry_after' => 90,
            'after_commit' => true,
        ],

        'beanstalkd' => [
            'driver' => 'beanstalkd',
            'host' => 'localhost',
            'queue' => 'default',
            'retry_after' => 90,
            'block_for' => 0,
            'after_commit' => true,
        ],

        'redis' => [
            'driver' => 'redis',
            'connection' => 'default',
            'queue' => 'medium',
            'retry_after' => 60 * 60 * 3.5, // 3.5 hours. The `queue:listen` timeout needs to be less than this.
            'after_commit' => true,
        ],

        'sqs' => [
            'driver' => 'sqs',
            'key' => env('S3_ACCESS_KEY'),
            'secret' => env('S3_SECRET_KEY'),
            'prefix' => 'https://sqs.'.env('SQS_REGION').'.amazonaws.com/'.env('AWS_ACCOUNT_ID'),
            'queue' => '',
            'suffix' => env('SQS_SUFFIX'),
            'region' => env('SQS_REGION', 'us-east-1'),
            'after_commit' => true,
        ],

        'aws_account_id' => env('AWS_ACCOUNT_ID'),

    ], collect(explode(',', env('AF_REGIONS')))->mapWithKeys(function ($region) {
        $uppercaseRegion = strtoupper($region);

        return [
            "sqs-{$region}" => [
                'driver' => 'sqs',
                'key' => env('S3_ACCESS_KEY'),
                'secret' => env('S3_SECRET_KEY'),
                'prefix' => 'https://sqs.'.env("S3_REGION_{$uppercaseRegion}", '').'.amazonaws.com/'.env('AWS_ACCOUNT_ID'),
                'queue' => '',
                'region' => env("S3_REGION_{$uppercaseRegion}", ''),
                'after_commit' => true,
            ],
        ];
    })->all()),

    /*
    |--------------------------------------------------------------------------
    | Failed Queue Jobs
    |--------------------------------------------------------------------------
    |
    | These options configure the behavior of failed queue job logging so you
    | can control which database and table are used to store the jobs that
    | have failed. You may change them to any database / table you wish.
    |
    */

    'failed' => [
        'driver' => env('QUEUE_FAILED_DRIVER'),
        'database' => null,
        'table' => null,
    ],

    'regions' => collect(explode(',', env('AF_REGIONS')))->mapWithKeys(function ($region) {
        return [$region => ['files' => "files-{$region}"]];
    })->merge([
        'eu' => ['files' => 'files-ie'], //TODO: why does the eu queue break the naming pattern?
    ])->all(),

    'queues' => [
        'assignments' => 'assignments',
        'bulk-downloads' => env('SQS_BULK_DOWNLOADS', 'speeder'),
        'bulk-downloads-complete' => env('SQS_BULK_DOWNLOADS_COMPLETE', 'speeder-completed'),
    ],
];
