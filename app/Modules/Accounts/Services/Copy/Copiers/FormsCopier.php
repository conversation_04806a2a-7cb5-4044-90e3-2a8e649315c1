<?php

namespace AwardForce\Modules\Accounts\Services\Copy\Copiers;

use AwardForce\Library\Database\Eloquent\Repository;
use AwardForce\Modules\Content\Blocks\Contracts\ContentBlockRepository;
use AwardForce\Modules\Forms\Forms\Bus\CreateForm;
use AwardForce\Modules\Forms\Forms\Bus\UpdateForm;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Database\Repositories\FormRepository;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Support\Arr;

class FormsCopier extends BaseCopier implements Copier, CopyMapper
{
    use CopyMapping;
    use DispatchesJobs;

    public function key(): string
    {
        return 'forms';
    }

    public function export(?int $seasonId = null, ?int $formId = null): void
    {
        if (is_null($formId)) {
            return;
        }
        $form = $this->repository()->configurationExport($seasonId, $formId);

        $this->setExported($form->id, $form->toArray());
    }

    public function dependencies(): array
    {
        return [
            SeasonCopier::class,
            ChaptersCopier::class,
            ContentBlocksCopier::class,
        ];
    }

    public function import(
        array $data,
        ?int $seasonId = null,
        ?int $formId = null
    ): void {
        if (feature_enabled('multiform')) {
            $form = $this->create($seasonId, $data);
            $updateData = array_only($data, ['chapterOption', 'chapters', 'contentblockId', 'type', 'settings', 'roles']);
        }
        $this->update($form ??= $this->repository()->requireById($formId), $updateData ?? $data);

        $this->mapImportedId($data['id'], $form->id);
        if (! empty($data['files'])) {
            $this->copyFiles($data['files'], $form->id);
        }
    }

    public function repository(): ?Repository
    {
        return app(FormRepository::class);
    }

    public function optionalDependencies(): array
    {
        return [
            ChaptersCopier::class,
        ];
    }

    private function create(int $seasonId, array $data): Form
    {
        return $this->dispatchSync(new CreateForm(
            $data['type'],
            $this->mapTranslations($data['translations']),
            Arr::get($data, 'settings', []),
            $data['order'] ?? 0,
            $seasonId
        ));
    }

    private function update(Form $form, array $data): void
    {
        $contentBlock = $data['contentblockId'] ? $this->contentBlockId($data['contentblockId']) : null;

        $this->dispatchSync(new UpdateForm(
            $form,
            Arr::get($data, 'settings', []),
            $data['type'] ?? null,
            $data['chapterOption'],
            $this->chapterIds($data['chapters']),
            $this->mapTranslations($data['translations'] ?? []),
            collect($this->roundIds($data['rounds'] ?? [])),
            $data['order'] ?? 0,
            $contentBlock ? slug_from_id($contentBlock, app(ContentBlockRepository::class)) : null,
            roles: $this->roleSlugsFromIds(Arr::get($data, 'roles', [])),
        ));
    }
}
