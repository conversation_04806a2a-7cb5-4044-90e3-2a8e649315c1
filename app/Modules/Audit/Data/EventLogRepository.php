<?php

namespace AwardForce\Modules\Audit\Data;

use Platform\Database\Eloquent\Collection;

interface EventLogRepository
{
    /**
     * Return the last 25 event logs for a given user.
     */
    public function forUser(int $userId): Collection;

    /**
     * Return the event logs for a given resource.
     */
    public function forResource(string $resource, ?int $foreignId = null): Collection;

    /**
     * yield a generator with unique user_ids with activity in provided season
     */
    public function userActivityInSeason(int $seasonId): \Generator;

    /*
     * Deletes event logs for a user in the current account
     */
    public function deleteForUser(int $userId): int;

    /*
     * Deletes event logs for a given account
     */
    public function deleteForAccount(int $accountId): int;
}
