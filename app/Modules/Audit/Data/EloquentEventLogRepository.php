<?php

namespace AwardForce\Modules\Audit\Data;

use AwardForce\Library\Database\Eloquent\Repository;
use AwardForce\Modules\Exports\Models\Exportable;
use Platform\Database\Eloquent\Collection;

class EloquentEventLogRepository extends Repository implements EventLogRepository
{
    use Exportable;

    /**
     * EloquentEventLogRepository constructor.
     */
    public function __construct(EventLog $model)
    {
        $this->setModel($model);
    }

    /**
     * Return the last 25 event logs for a given user.
     */
    public function forUser(int $userId): Collection
    {
        return $this->getQuery()
            ->whereUserId($userId)
            ->orderBy('created_at', 'desc')
            ->take(50)
            ->get();
    }

    /**
     * Return the event logs for a given resource.
     */
    public function forResource(string $resource, ?int $foreignId = null): Collection
    {
        $query = $this->getQuery()
            ->whereResource($resource)
            ->orderBy('created_at', 'desc');

        if ($foreignId) {
            $query = $query->whereForeignId($foreignId);
        }

        return $query->take(50)->get();
    }

    public function userActivityInSeason(int $seasonId): \Generator
    {
        // TODO: Implement userActivityInSeason() method.
    }

    public function deleteForUser(int $userId): int
    {
        // TODO: Implement deleteForUser() method.
    }

    public function deleteForAccount(int $accountId): int
    {
        // TODO: Implement deleteForAccount() method.
    }
}
