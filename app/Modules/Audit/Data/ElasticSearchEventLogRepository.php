<?php

namespace AwardForce\Modules\Audit\Data;

use AwardForce\Library\Database\ElasticSearch\Repository;
use AwardForce\Modules\Exports\Models\Exportable;
use Platform\Database\Eloquent\Collection;

class ElasticSearchEventLogRepository extends Repository implements EventLogRepository
{
    use Exportable;

    /**
     * EloquentEventLogRepository constructor.
     */
    public function __construct(EventLog $model)
    {
        $this->setModel($model);
    }

    /**
     * Return the last 25 event logs for a given user.
     */
    public function forUser(int $userId): Collection
    {
        return new Collection($this->getQuery()
            ->where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->take(50)
            ->get());
    }

    /**
     * Return the event logs for a given resource.
     */
    public function forResource(string $resource, ?int $foreignId = null): Collection
    {
        $query = $this->getQuery()
            ->where('resource', $resource)
            ->orderBy('created_at', 'desc');

        if ($foreignId) {
            $query = $query->where('foreign_id', $foreignId);
        }

        return new Collection($query->take(50)->get());
    }

    public function userActivityInSeason(int $seasonId): \Generator
    {
        $query = $this->getQuery()
            ->where('season_id', $seasonId)
            ->select('user_id')
            ->take(1000)
            ->scroll('10m');
        $results = $query->get();
        $scrollId = null;

        while ($results->count()) {
            $scrollId = $results->scroll_id;

            yield $results->pluck('user_id')->unique()->filter();

            $results = $this->getQuery()
                ->scroll('10m')
                ->scrollID($scrollId)
                ->get();
        }
        if ($scrollId) {
            $this->getQuery()
                ->scrollID($scrollId)
                ->clear();
        }
    }

    public function deleteForUser(int $userId): int
    {
        $parameters = $this->getModel()->defaultRawQueryParameters();

        $parameters['body'] = [
            'query' => [
                'bool' => [
                    'must' => [
                        ['match' => ['foreign_id' => $userId]],
                        ['match' => ['resource' => 'user']],
                        ['match' => ['account_id' => $this->currentAccountId()]],
                    ],
                ],
            ],

        ];

        return $this->getQuery()->raw()->deleteByQuery($parameters)['deleted'] ?? 0;
    }

    public function deleteForAccount(int $accountId): int
    {
        $parameters = $this->getModel()->defaultRawQueryParameters();

        $parameters['body'] = [
            'query' => [
                'bool' => [
                    'must' => [
                        ['match' => ['account_id' => $accountId]],
                    ],
                ],
            ],

        ];

        return $this->getQuery()->raw()->deleteByQuery($parameters)['deleted'] ?? 0;
    }
}
