<?php

namespace AwardForce\Modules\Audit;

use AwardForce\Library\ServiceProvider;
use AwardForce\Modules\Accounts\Events\AccountWasDestroyed;
use AwardForce\Modules\Audit\Data\ElasticSearchEventLogRepository;
use AwardForce\Modules\Audit\Data\EventLogRepository;
use AwardForce\Modules\Audit\Events\SystemResource;
use AwardForce\Modules\Audit\Listeners\AuditListener;

class AuditServiceProvider extends ServiceProvider
{
    protected $repositories = [
        EventLogRepository::class => ElasticSearchEventLogRepository::class,
    ];
    protected $listeners = [
        AccountWasDestroyed::class => AuditListener::class.'@whenAccountWasDestroyed',
    ];

    public function boot()
    {
        $this->bootResources();
    }

    private function bootResources()
    {
        SystemResource::setResources($this->app['config']->get('audit.resources', []));
    }
}
