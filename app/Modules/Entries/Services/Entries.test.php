<?php

namespace AwardForce\Modules\Entries\Services;

use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Entries\Contracts\AttachmentRepository;
use AwardForce\Modules\Entries\Contracts\EntryRepository;
use AwardForce\Modules\Entries\Events\EntryWasCreated;
use AwardForce\Modules\Entries\Events\EntryWasUpdated;
use AwardForce\Modules\Entries\Events\TitleWasChanged;
use AwardForce\Modules\Entries\Models\Attachment;
use AwardForce\Modules\Entries\Models\Contributor;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Collaboration\Repositories\CollaboratorsRepository;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use AwardForce\Modules\Identity\Users\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Mockery as m;
use Platform\Test\EventAssertions;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;
use Tests\Library\ArrayAssertions;
use Tests\Support\UsesEncrypter;

final class EntriesTest extends BaseTestCase
{
    use ArrayAssertions,
        Database,
        EventAssertions,
        Laravel,
        UsesEncrypter;

    /**
     * @var m\MockInterface
     */
    protected $contributors;

    protected ValuesService $values;

    /**
     * @var m\MockInterface
     */
    protected $entries;

    /**
     * @var Entry
     */
    protected $entry;

    /**
     * @var Entries
     */
    protected $service;

    /**
     * @var AttachmentRepository
     */
    private $attachments;

    public function init()
    {
        $this->mockEncrypter();

        $this->values = app(ValuesService::class);
        $this->entries = app(EntryRepository::class);
        $this->attachments = app(AttachmentRepository::class);
        $this->collaborators = app(CollaboratorsRepository::class);

        $this->entry = $this->muffin(Entry::class);
        $this->user = $this->muffin(User::class);

        $this->service = new Entries($this->attachments, $this->collaborators, $this->values, $this->entries);
    }

    public function testEntryStartUpdateReadValues(): void
    {
        $category = $this->muffin(Category::class);
        $forms = $this->muffins(2, Form::class);
        $seasonId = 100;
        $chapterId = 101;
        $categoryId = $category->id;

        // define come fields
        $fields = [
            $this->muffin(Field::class, ['season_id' => $seasonId, 'form_id' => $forms[0]->id, 'resource' => Field::RESOURCE_FORMS, 'type' => 'text', 'protection' => Field::PROTECTION_STANDARD]),
            $this->muffin(Field::class, ['season_id' => $seasonId, 'form_id' => $forms[0]->id, 'resource' => Field::RESOURCE_FORMS, 'type' => 'text', 'protection' => Field::PROTECTION_ELEVATED]),
            $this->muffin(Field::class, ['season_id' => $seasonId, 'form_id' => $forms[0]->id, 'resource' => Field::RESOURCE_FORMS, 'type' => 'text', 'protection' => Field::PROTECTION_MAXIMUM]),
            $this->muffin(Field::class, ['season_id' => $seasonId, 'form_id' => $forms[0]->id, 'resource' => Field::RESOURCE_FORMS, 'type' => 'text', 'protection' => Field::PROTECTION_STANDARD, 'entrant_write_access' => false]),
            $this->muffin(Field::class, ['season_id' => $seasonId, 'form_id' => $forms[0]->id, 'category_option' => Field::CATEGORY_OPTION_SELECT, 'resource' => Field::RESOURCE_FORMS, 'type' => 'text', 'protection' => Field::PROTECTION_STANDARD]),
            $this->muffin(Field::class, ['season_id' => $seasonId, 'form_id' => $forms[0]->id, 'category_option' => Field::CATEGORY_OPTION_SELECT, 'resource' => Field::RESOURCE_FORMS, 'type' => 'text', 'protection' => Field::PROTECTION_STANDARD]),
            $this->muffin(Field::class, ['season_id' => $seasonId + 1, 'form_id' => $forms[1]->id, 'resource' => Field::RESOURCE_FORMS, 'type' => 'text', 'protection' => Field::PROTECTION_STANDARD]),
            $this->muffin(Field::class, ['season_id' => $seasonId, 'form_id' => $forms[0]->id, 'resource' => Field::RESOURCE_USERS, 'type' => 'text', 'protection' => Field::PROTECTION_STANDARD]),
        ];

        // attach one field to a category
        $fields[5]->categories()->attach($category);

        // set values for defined fields
        $values = [
            'unknown-field-slug' => 'invisible -> unknown slug',
            ((string) $fields[0]->slug) => 'visible unprotected',
            ((string) $fields[1]->slug) => 'visible protection evelated',
            ((string) $fields[2]->slug) => 'visible protection maximum',
            ((string) $fields[3]->slug) => 'invisible -> no entrant access',
            ((string) $fields[4]->slug) => 'invisible -> no category relstion',
            ((string) $fields[5]->slug) => 'visible with category relstion',
            ((string) $fields[6]->slug) => 'invisible -> wrong season',
            ((string) $fields[7]->slug) => 'invisible -> non-entry',
        ];
        $this->encrypter->shouldReceive('encrypt')->with($values[(string) $fields[1]->slug])->andReturn($values[(string) $fields[1]->slug]);
        $this->encrypter->shouldReceive('decrypt')->with($values[(string) $fields[1]->slug])->andReturn($values[(string) $fields[1]->slug]);
        $this->encrypter->shouldReceive('maximum')->with($values[(string) $fields[2]->slug])->andReturn($values[(string) $fields[2]->slug]);
        $this->encrypter->shouldReceive('decrypt')->with($values[(string) $fields[2]->slug])->andReturn($values[(string) $fields[2]->slug]);

        // create an entry
        $entry = $this->service->startEntry(
            $this->user->id,
            'some title',
            $seasonId,
            $forms[0]->id,
            $chapterId,
            $categoryId,
            $values,
            true
        );

        // check if expected events were released
        $this->assertRaised($entry, EntryWasCreated::class);

        // check if entry was saved with an expected title
        $this->assertEquals('some title', $entry->title);

        // ger saved entry fieldsValues
        $entryValues = $this->values->getValuesForObject($entry);

        // check if an entry has expected number of fieldValues
        $this->assertCount(4, $entryValues);

        // check if all entry fieldsValues are expected to be visible
        $this->assertCount(
            4,
            collect($entryValues)->filter(
                function (string $value) {
                    return substr($value, 0, 7) === 'visible';
                }
            )
        );

        // change a category id
        $categoryId = $this->muffin(Category::class)->id;

        // update an entry withch changed category and title
        //   and unchanged fieldsValues set
        $entry = $this->service->updateEntry(
            $entry->id,
            $this->user->id,
            'some other title',
            $chapterId,
            $categoryId,
            true,
            null,
            Carbon::now()->timestamp
        );

        // check if proper events were released
        $this->assertRaised($entry, EntryWasUpdated::class);

        // check if title was updated
        $this->assertEquals('some other title', $entry->title);

        // get fieldsValues out of updated entry
        $newEntryValues = $this->values->getValuesForObject($entry);

        // check values still remain
        $this->assertCount(4, $newEntryValues);

        // update an entry again setting empty fieldValues and a new title
        $entry = $this->service->updateEntry(
            $entry->id,
            $this->user->id,
            'title for entry without values',
            $chapterId,
            $categoryId,
            true,
            []
        );

        // check if expected event was released
        $eventsDispatched = $entry->releaseEvents();
        $this->assertCount(2, $eventsDispatched);
        $this->assertArrayContainsObject($eventsDispatched, EntryWasUpdated::class);
        $this->assertArrayContainsObject($eventsDispatched, TitleWasChanged::class);

        // check if title was properly changed
        $this->assertEquals('title for entry without values', $entry->title);

        // retrieve fieldsValues out of updated entry
        $emptiedEntryValues = $this->values->getValuesForObject($entry);

        // check values still remain
        $this->assertCount(4, $newEntryValues);
    }

    public function testStartAndUpdateEntryContributor(): void
    {
        $form = FormSelector::get();
        $tab = $this->muffin(Tab::class, ['form_id' => $form->id, 'type' => Tab::TYPE_CONTRIBUTORS]);

        // get some fields
        $fields = $this->muffins(3, Field::class, [
            'resource' => Field::RESOURCE_CONTRIBUTORS,
            'tab_id' => $tab->id,
            'form_id' => $form->id,
        ]);

        // and set values for those fields
        $values = [
            ((string) $fields[0]->slug) => 'first',
            ((string) $fields[1]->slug) => 'second',
            ((string) $fields[2]->slug) => [1, 2, 3, 4],
        ];

        // get an entry
        $entry = $this->muffin(Entry::class, ['form_id' => $form->id]);

        // start some contributors for that entry
        //   with values
        $this->service->createContributor($entry, $values, $tab->id);
        $this->service->createContributor($entry, $values, $tab->id);
        $this->service->createContributor($entry, $values, $tab->id);

        // check if a contributors have been added to entry
        $this->assertCount(3, $entry->fresh()->contributors);

        // and if they all have values set properly
        $this->assertCount(
            3,
            $entry->contributors->filter(
                function (Contributor $contributor) use ($values) {
                    $contributorEntryValues = $this->values->getValuesForObject($contributor)->toArray();

                    return sort($values) === sort($contributorEntryValues);
                }
            )
        );

        // prepare contributor->values structure
        $contributorsWithValues = $entry->contributors->map(
            function (Contributor $contributor) use ($fields) {
                return [
                    'key' => $contributor->id,
                    'values' => [
                        ((string) $fields[0]->slug) => $contributor->id,
                        ((string) $fields[1]->slug) => '',
                        ((string) $fields[2]->slug) => ['a', 9],
                    ],
                ];
            }
        )->pluck('values', 'key')->toArray();
        $contributorsWithValues['aaa'] = array_pop($contributorsWithValues);

        // and update entry contributors
        $this->service->updateContributors($entry, $contributorsWithValues);

        // check if a contributors have been added to entry
        $this->assertCount(2, ($entry = $entry->fresh())->contributors);

        // and if they all have values set properly
        $this->assertCount(
            2,
            $entry->fresh()->contributors->filter(
                function (Contributor $contributor) use ($contributorsWithValues, &$first) {
                    $contributorEntryValues = $this->values->getValuesForObject($contributor)->toArray();
                    $values = Arr::get($contributorsWithValues, $contributor->id, []);
                    foreach ($values as $fieldSlug => $value) {
                        if ($value != $contributorEntryValues[$fieldSlug]) {
                            return false;
                        }
                    }

                    return true;
                }
            )
        );
    }

    public function testSetEntryAttachments(): void
    {
        // get some fields
        $field = $this->muffin(Field::class, ['resource' => Field::RESOURCE_ATTACHMENTS]);

        // instantiate an entry with attachments
        $entry = $this->muffin(Entry::class);
        //$entry->attachments()->create([]);
        //$entry->attachments()->create([]);

        $attachments = $this->muffins(2, Attachment::class, ['submittable_id' => $entry->id]);

        // prepare attachments->values structure
        $attachmentsWithValues = collect($attachments)->map(
            function (Attachment $attachment) use ($field) {
                return [
                    'key' => $attachment->fileId,
                    'values' => [
                        ((string) $field->slug) => [$attachment->id, $attachment->fileId],
                    ],
                ];
            }
        )->pluck('values', 'key')->toArray();

        // yet one more attachment w/o any values
        //$entry->attachments()->create([]);
        $attachments[] = $this->muffin(Attachment::class);

        $entry->attachments()->saveMany($attachments);

        // and update entry attachments
        $this->service->setSubmittableAttachments($entry->id, $attachmentsWithValues);

        // and if attachments all have values set properly
        $this->assertCount(
            3,
            $entry->attachments->filter(
                function (Attachment $attachment) use ($attachmentsWithValues) {
                    $attachmentEntryValues = $this->values->getValuesForObject($attachment)->toArray();
                    $values = $attachmentsWithValues[$attachment->fileId] ?? [];

                    return sort($values) === sort($attachmentEntryValues);
                }
            )
        );
    }

    public function testAttachmentsOrderingSingleAttachmentsTab(): void
    {
        $tab = $this->muffin(Tab::class, ['resource' => Tab::RESOURCE_ENTRIES, 'type' => Tab::TYPE_ATTACHMENTS]);

        $attachments = collect([
            $this->muffin(Attachment::class, ['tab_id' => $tab->id, 'file_id' => 101, 'order' => 11]),
            $this->muffin(Attachment::class, ['tab_id' => $tab->id, 'file_id' => 102, 'order' => 12]),
            $this->muffin(Attachment::class, ['tab_id' => $tab->id, 'file_id' => 103, 'order' => 13]),
            $this->muffin(Attachment::class, ['tab_id' => $tab->id, 'file_id' => 104, 'order' => 14]),
        ]);

        $this->service->orderAttachments([
            102 => 4,
            103 => 3,
            101 => 2,
            666 => 1, // Non-existing file id
            104 => 999,
        ]);

        $attachments = $this->attachments->getByFileIds([103, 102, 101, 666, 104]);

        $this->assertSame(
            [
                101 => 1,
                103 => 2,
                102 => 3,
                104 => 4,
            ],
            $attachments->sortBy('order')->pluck('order', 'file_id')->toArray()
        );
    }

    public function testAttachmentsOrderingMultipleAttachmentsTab(): void
    {
        $tabA = $this->muffin(Tab::class, ['resource' => Tab::RESOURCE_ENTRIES, 'type' => Tab::TYPE_ATTACHMENTS]);
        $tabB = $this->muffin(Tab::class, ['resource' => Tab::RESOURCE_ENTRIES, 'type' => Tab::TYPE_ATTACHMENTS]);

        $attachments = collect([
            $this->muffin(Attachment::class, ['tab_id' => $tabA->id, 'file_id' => 101, 'order' => 1]),
            $this->muffin(Attachment::class, ['tab_id' => $tabA->id, 'file_id' => 102, 'order' => 2]),
            $this->muffin(Attachment::class, ['tab_id' => $tabA->id, 'file_id' => 103, 'order' => 3]),
            $this->muffin(Attachment::class, ['tab_id' => $tabA->id, 'file_id' => 104, 'order' => 9]),
            $this->muffin(Attachment::class, ['tab_id' => $tabB->id, 'file_id' => 105, 'order' => 1]),
            $this->muffin(Attachment::class, ['tab_id' => $tabB->id, 'file_id' => 106, 'order' => 2]),
        ]);

        $this->service->orderAttachments([
            101 => 1,
            102 => 3,
            103 => 2,
            104 => 4,
            105 => 2,
            106 => 1,
        ]);

        $attachmentsTabA = $this->attachments->getByFileIds(range(101, 104, 1));
        $attachmentsTabB = $this->attachments->getByFileIds(range(105, 106, 1));

        $this->assertSame(
            [
                101 => 1,
                103 => 2,
                102 => 3,
                104 => 4,
            ],
            $attachmentsTabA->sortBy('order')->pluck('order', 'file_id')->toArray()
        );
        $this->assertSame(
            [
                106 => 1,
                105 => 2,
            ],
            $attachmentsTabB->sortBy('order')->pluck('order', 'file_id')->toArray()
        );
    }

    public function testModifyJustEntryTitleManager(): void
    {
        app(Entries::class)->updateEntry(
            $this->entry->id,
            $this->entry->userId,
            $newTitle = Str::random(5),
            $this->entry->chapterId,
            $this->entry->categoryId,
            false,
            $this->entry->fieldValues,
            Carbon::now()->timestamp
        );

        $this->assertEquals($title = app(EntryRepository::class)->getById($this->entry->id)->title, $newTitle);
        $this->assertNotEquals($title, $this->entry->title);
    }

    public function testModifyJustEntryTitleEntrant(): void
    {
        app(Entries::class)->updateEntry(
            $this->entry->id,
            $this->entry->userId,
            $newTitle = Str::random(5),
            $this->entry->chapterId,
            $this->entry->categoryId,
            false,
            $this->entry->fieldValues,
            Carbon::now()->timestamp
        );

        $this->assertEquals($title = app(EntryRepository::class)->getById($this->entry->id)->title, $newTitle);
        $this->assertNotEquals($title, $this->entry->title);
    }

    public function testResetInvitedStatusOnFirstUpdate(): void
    {
        $entry = $this->muffin(Entry::class, ['invited_at' => now()]);
        $this->assertEquals('invited', $entry->submissionStatus());

        app(Entries::class)->updateEntry(
            $entry->id,
            $entry->userId,
            $entry->title,
            $entry->chapterId,
            $entry->categoryId,
            false,
            $entry->fieldValues,
            Carbon::now()->timestamp
        );

        $this->assertEquals('in_progress', $entry->fresh()->submissionStatus());
    }

    public function test_update_single_contributor_works()
    {
        $contributor = $this->muffin(Contributor::class);
        $field = $this->muffin(Field::class, [
            'resource' => Field::RESOURCE_CONTRIBUTORS,
            'tab_id' => $contributor->tab_id,
        ]);

        $fieldSlug = (string) $field->slug;

        $this->service->updateContributor($contributor, [
            $fieldSlug => 'Test',
        ]);

        $values = $this->values->getValuesForObject($contributor);

        $this->assertEquals($values[$fieldSlug], 'Test');
    }
}
