<?php

namespace AwardForce\Modules\Ecommerce\Cart;

use AwardForce\Library\Values\Currency;
use AwardForce\Modules\Ecommerce\Cart\Costing\EntryAmount;
use AwardForce\Modules\Ecommerce\Cart\Costing\Pricing\Price;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Features\Facades\Feature;
use AwardForce\Modules\Forms\Forms\Database\Entities\FormSettings;
use Illuminate\Support\Str;
use Mockery as m;
use PHPUnit\Framework\Attributes\TestWith;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class EntryItemTest extends BaseTestCase
{
    use Database;
    use Laravel;

    #[TestWith([true])]
    #[TestWith([false])]
    public function testChapterOnlyAppearsInDescriptionOnMultiChapterAccounts(bool $multiChapter)
    {
        Feature::shouldReceive('enabled')->with('multi_chapter')->andReturn($multiChapter);

        $entryItem = new EntryItem(
            new EntryAmount($entry = $this->muffin(Entry::class), new Currency('AUD')),
            Str::random(),
            m::mock(Price::class),
            false
        );

        $entry->form->settings = FormSettings::create(['displayId' => true]);
        $entry->chapter->saveTranslation('en_GB', 'name', $name = 'aBcD 123', $entry->accountId);

        $this->assertSame($multiChapter, Str::contains($entryItem->description(), $name));
    }
}
