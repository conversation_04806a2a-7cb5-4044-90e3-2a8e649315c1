<?php

namespace AwardForce\Modules\Billing\Data;

use AwardForce\Modules\Billing\Enums\Status;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class ElasticSearchUsageLogRepositoryTest extends BaseTestCase
{
    use Database;
    use Laravel;

    private ElasticSearchUsageLogRepository $repository;

    protected function init(): void
    {
        UsageLog::all()->each->delete();
        UsageLog::refreshIndex();
        $this->repository = new ElasticSearchUsageLogRepository(new UsageLog());
    }

    public function testUpdateStatusByIdsWithSingleId(): void
    {
        $usageLog = $this->muffin(UsageLog::class, ['status' => Status::Processing]);
        UsageLog::refreshIndex();

        $this->repository->updateStatusByIds([$usageLog->getID()], Status::Synced);
        UsageLog::refreshIndex();

        $updatedLog = UsageLog::find($usageLog->getID());
        $this->assertEquals(Status::Synced, $updatedLog->status);
        $this->assertNull($updatedLog->processingId);
    }

    public function testUpdateStatusByIdsWithMultipleIds(): void
    {
        $usageLogs = $this->muffins(3, UsageLog::class, ['status' => Status::Processing]);
        $unchangedLog = $this->muffin(UsageLog::class, ['status' => Status::Ready]);
        UsageLog::refreshIndex();

        $ids = collect($usageLogs)->just('_id');
        $this->repository->updateStatusByIds($ids, Status::Failed);
        UsageLog::refreshIndex();

        foreach ($usageLogs as $usageLog) {
            $updatedLog = UsageLog::find($usageLog->getID());
            $this->assertEquals(Status::Failed, $updatedLog->status);
            $this->assertNull($updatedLog->processingId);
        }

        // Verify unchanged log remains unchanged
        $unchangedLogAfter = UsageLog::find($unchangedLog->getID());
        $this->assertEquals(Status::Ready, $unchangedLogAfter->status);
    }

    public function testResetBatchToReady(): void
    {
        $processingId = str_random();

        $logsToReset = $this->muffins(2, UsageLog::class, [
            'status' => Status::Processing,
            'processing_id' => $processingId,
        ]);

        $otherProcessingLog = $this->muffin(UsageLog::class, [
            'status' => Status::Processing,
            'processing_id' => 'other-id',
        ]);

        $readyLog = $this->muffin(UsageLog::class, ['status' => Status::Ready]);
        UsageLog::refreshIndex();

        $this->repository->resetBatchToReady($processingId);
        UsageLog::refreshIndex();

        // Verify logs with matching processing_id are reset
        foreach ($logsToReset as $log) {
            $updatedLog = UsageLog::find($log->getID());
            $this->assertEquals(Status::Ready, $updatedLog->status);
            $this->assertNull($updatedLog->processingId);
        }

        // Verify other logs remain unchanged
        $otherLogAfter = UsageLog::find($otherProcessingLog->getID());
        $this->assertEquals(Status::Processing, $otherLogAfter->status);
        $this->assertEquals('other-id', $otherLogAfter->processingId);

        $readyLogAfter = UsageLog::find($readyLog->getID());
        $this->assertEquals(Status::Ready, $readyLogAfter->status);
    }

    public function testClaimEventsForProcessingSuccess(): void
    {
        $processingId = 'claim-test-id-' . uniqid();
        $accountId1 = 123;
        $accountId2 = 456;
        $otherAccountId = 789;

        // Create logs for target accounts
        $targetLogs = [
            $this->muffin(UsageLog::class, ['status' => Status::Ready, 'account_id' => $accountId1]),
            $this->muffin(UsageLog::class, ['status' => Status::Ready, 'account_id' => $accountId2]),
        ];

        // Create logs that should not be claimed
        $otherLogs = [
            $this->muffin(UsageLog::class, ['status' => Status::Ready, 'account_id' => $otherAccountId]),
            $this->muffin(UsageLog::class, ['status' => Status::Processing, 'account_id' => $accountId1]),
            $this->muffin(UsageLog::class, ['status' => Status::Synced, 'account_id' => $accountId1]),
        ];

        UsageLog::refreshIndex();

        $claimedEvents = $this->repository->claimEventsForProcessing(
            $processingId,
            [$accountId1, $accountId2],
            10
        );
        UsageLog::refreshIndex();

        // Verify the correct number of events was returned
        $this->assertCount(2, $claimedEvents);

        // Verify returned events have the correct status and processing_id
        foreach ($claimedEvents as $event) {
            $this->assertEquals(Status::Processing, $event->status);
            $this->assertEquals($processingId, $event->processingId);
            $this->assertContains($event->accountId, [$accountId1, $accountId2]);
        }

        // Verify target logs are claimed in the database
        foreach ($targetLogs as $log) {
            $updatedLog = UsageLog::find($log->getID());
            $this->assertEquals(Status::Processing, $updatedLog->status);
            $this->assertEquals($processingId, $updatedLog->processingId);
        }

        // Verify other logs remain unchanged
        foreach ($otherLogs as $log) {
            $updatedLog = UsageLog::find($log->getID());
            $this->assertEquals($log->status, $updatedLog->status);
            $this->assertNotEquals($processingId, $updatedLog->processingId);
        }
    }

    public function testClaimEventsForProcessingRespectsMaxEvents(): void
    {
        $processingId = 'limit-test-id-' . uniqid();
        $accountId = 123;
        $maxEvents = 2;

        // Create more logs than the limit
        $readyLogs = $this->muffins(5, UsageLog::class, [
            'status' => Status::Ready,
            'account_id' => $accountId,
        ]);
        UsageLog::refreshIndex();

        $claimedEvents = $this->repository->claimEventsForProcessing($processingId, [$accountId], $maxEvents);
        UsageLog::refreshIndex();

        // Should return exactly the max events limit
        $this->assertCount($maxEvents, $claimedEvents);

        // Verify all returned events have correct status and processing_id
        foreach ($claimedEvents as $event) {
            $this->assertEquals(Status::Processing, $event->status);
            $this->assertEquals($processingId, $event->processingId);
            $this->assertEquals($accountId, $event->accountId);
        }

        // Count how many were actually claimed in the database
        $claimedCount = 0;
        foreach ($readyLogs as $log) {
            $updatedLog = UsageLog::find($log->getID());
            if ($updatedLog->status === Status::Processing && $updatedLog->processingId === $processingId) {
                $claimedCount++;
            }
        }

        // Should respect the max events limit
        $this->assertEquals($maxEvents, $claimedCount);
    }
}
