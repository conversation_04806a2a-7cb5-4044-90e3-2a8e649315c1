<?php

use AwardForce\Modules\Billing\Http\Controllers\Api\Internal\UsageEventController;
use AwardForce\Modules\Billing\Http\Middleware\VerifySharedSecret;
use Illuminate\Support\Facades\Route;

/**
 * Internal API routes for external services to interact with our system.
 * These routes are intended for server-to-server communication.
 */
Route::post('usage-events', [UsageEventController::class, 'store'])
    ->name('api.internal.billing.usage-events.store')
    ->middleware(VerifySharedSecret::class);
