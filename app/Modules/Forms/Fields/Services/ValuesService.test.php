<?php

namespace AwardForce\Modules\Forms\Fields\Services;

use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Entries\Contracts\EntryRepository;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Entries\Repositories\EloquentEntryRepository;
use AwardForce\Modules\Files\Contracts\FileRepository;
use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Files\Repositories\EloquentFileRepository;
use AwardForce\Modules\Forms\Fields\Database\DataAccess\Fields;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Database\Repositories\FieldRepository;
use AwardForce\Modules\Forms\Fields\Events\UpdatingFieldValuesAfterEncoding;
use AwardForce\Modules\Forms\Fields\Events\UpdatingFieldValuesBeforeEncoding;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use AwardForce\Modules\Seasons\Models\Season;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Mockery as m;
use Platform\Encryption\Encrypter;
use Tests\IntegratedTestCase;
use Tests\Support\UsesEncrypter;
use Tests\TestsPrivateMethods;

final class ValuesServiceTest extends IntegratedTestCase
{
    use TestsPrivateMethods;
    use UsesEncrypter;

    /** @var ValuesService */
    private $valuesService;

    /** @var FieldRepository */
    private $fields;

    /** @var EntryRepository */
    private $entries;

    public function init()
    {
        $this->mockEncrypter();
        $this->entries = app(EntryRepository::class);
        $this->valuesService = app(ValuesService::class);
        $this->fields = app(FieldRepository::class);

        Field::unguard();
    }

    protected function tearDown(): void
    {
        parent::tearDown();
    }

    public function testLeaveOthersAlone(): void
    {
        $field1 = $this->addField('Entries');
        $field2 = $this->addField('Entries');
        $field3 = $this->addField('Entries');

        $entry1 = $this->muffin(Entry::class);
        $entry2 = $this->muffin(Entry::class);

        // Here we setup previously available field values for entry 2
        $values = [
            (string) $field1->slug => 'stuff',
            (string) $field2->slug => 'nothing',
            'lkjsdf' => 'nonsense',
        ];

        $this->valuesService->syncValuesForObject($values, $entry2);

        // Setup new values for entry 1 & 2
        $values1 = [
            (string) $field1->slug => 'value1',
            (string) $field2->slug => 'value2',
            'lkjsdf' => 'nonsense',
        ];

        $values2 = [
            (string) $field1->slug => 'value3',
            (string) $field2->slug => 'value4',
            'lkjsdf' => 'nonsense',
        ];

        $values3 = [
            (string) $field2->slug => 'value5',
            (string) $field3->slug => 'value6',
        ];

        $this->valuesService->syncValuesForObject($values1, $entry1);
        $this->valuesService->syncValuesForObject($values2, $entry2);

        $entry1Values = $this->valuesService->mapValuesToFields($entry1, new Fields([$field1, $field2, $field3]));
        $entry2Values = $this->valuesService->mapValuesToFields($entry2, new Fields([$field1, $field2, $field3]));

        $this->valuesService->syncValuesForObject($values3, $entry1);

        $updatedValues = $this->valuesService->mapValuesToFields($entry1, new Fields([$field1, $field2, $field3]));

        $this->assertSame(1, $entry1Values->where('value', 'value1')->count());
        $this->assertSame(1, $entry1Values->where('value', 'value2')->count());
        $this->assertSame(0, $entry1Values->where('value', 'value3')->count()); // Make sure it doesn't have an entry 2 field value

        $this->assertSame(1, $entry2Values->where('value', 'value3')->count());
        $this->assertSame(1, $entry2Values->where('value', 'value4')->count());
        $this->assertSame(0, $entry2Values->where('value', 'value1')->count()); // Make sure it doens't have an entry 1 field value

        $this->assertSame(1, $updatedValues->where('value', 'value5')->count());
        $this->assertSame(1, $updatedValues->where('value', 'value6')->count());
    }

    private function addField($resource, array $params = [])
    {
        $season = $this->muffin(Season::class);

        $field = new Field;
        $field->seasonId = $season->id;
        $field->tabId = 1;
        $field->formId = FormSelector::getId();
        $field->resource = $resource;
        $field->type = 'text';
        $field->options = null;
        $field->required = false;
        $field->includeTimezone = false;
        $field->maximumWords = null;
        $field->maximumCharacters = null;
        $field->searchable = false;
        $field->order = 2;
        $field->categoryOption = 'all';
        $field->roleOption = 'all';
        $field->registration = false;
        $field->autocomplete = false;
        $field->conditional = 'yeah';
        $field->conditionalVisibility = 'show';
        $field->conditionalFieldId = null;
        $field->conditionalPattern = '';
        $field->conditionalValue = '';
        $field->entrantReadAccess = true;
        $field->entrantWriteAccess = true;

        $field->fill($params);

        $this->fields->save($field);

        return $field;
    }

    public function testFilterEntrantsValuesOnlyForFormAndCategory(): void
    {
        $category = $this->muffin(Category::class);

        $categoryId = $category->id;

        // get some fields
        $fields = [
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'type' => 'text', 'protection' => Field::PROTECTION_STANDARD]),
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'type' => 'text', 'protection' => Field::PROTECTION_ELEVATED]),
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'type' => 'text', 'protection' => Field::PROTECTION_MAXIMUM]),
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'type' => 'text', 'protection' => Field::PROTECTION_STANDARD, 'entrant_write_access' => false]),
            $this->muffin(Field::class, ['category_option' => Field::CATEGORY_OPTION_SELECT, 'resource' => Field::RESOURCE_FORMS, 'type' => 'text', 'protection' => Field::PROTECTION_STANDARD]),
            $this->muffin(Field::class, ['category_option' => Field::CATEGORY_OPTION_SELECT, 'resource' => Field::RESOURCE_FORMS, 'type' => 'text', 'protection' => Field::PROTECTION_STANDARD]),
            $this->muffin(Field::class, ['form_id' => $category->formId + 1, 'resource' => Field::RESOURCE_FORMS, 'type' => 'text', 'protection' => Field::PROTECTION_STANDARD]),
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_USERS, 'type' => 'text', 'protection' => Field::PROTECTION_STANDARD]),
        ];

        // attach a field to category
        $fields[5]->categories()->attach($category);

        // set values for fields
        $values = [
            'unknown-field-slug' => 'invisible -> unknown slug',
            ((string) $fields[0]->slug) => 'visible unprotected',
            ((string) $fields[1]->slug) => 'visible protection evelated',
            ((string) $fields[2]->slug) => 'visible protection maximum',
            ((string) $fields[3]->slug) => 'invisible -> no entrant access',
            ((string) $fields[4]->slug) => 'invisible -> no category relstion',
            ((string) $fields[5]->slug) => 'visible with category relstion',
            ((string) $fields[6]->slug) => 'invisible -> wrong season',
            ((string) $fields[7]->slug) => 'invisible -> non-entry',
        ];
        $this->encrypter->shouldReceive('encrypt')->with($values[(string) $fields[1]->slug])->andReturn($values[(string) $fields[1]->slug]);
        $this->encrypter->shouldReceive('decrypt')->with($values[(string) $fields[1]->slug])->andReturn($values[(string) $fields[1]->slug]);
        $this->encrypter->shouldReceive('encrypt')->with($values[(string) $fields[2]->slug])->andReturn($values[(string) $fields[2]->slug]);
        $this->encrypter->shouldReceive('decrypt')->with($values[(string) $fields[2]->slug])->andReturn($values[(string) $fields[2]->slug]);

        // filter accesible values
        $filtered = $this->valuesService->filterEntrantsValuesOnlyForFormAndCategory($values, $category->formId, $categoryId);

        // and assert they match expectations
        $this->assertCount(4, $filtered);
        $this->assertCount(
            4,
            collect($filtered)->filter(
                function (string $value) {
                    return substr($value, 0, 7) === 'visible';
                }
            )
        );
    }

    public function testGetSetSyncValuesForObject(): void
    {
        // get some fields
        $fields = [
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'protection' => Field::PROTECTION_STANDARD, 'type' => 'table']),
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'protection' => Field::PROTECTION_ELEVATED, 'type' => 'content']),
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'protection' => Field::PROTECTION_MAXIMUM, 'type' => 'table']),
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'protection' => Field::PROTECTION_STANDARD]),
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'protection' => Field::PROTECTION_ELEVATED]),
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'protection' => Field::PROTECTION_MAXIMUM]),
        ];

        // hashes should be made for these
        $searchables = [
            (string) $fields[3]->slug,
            (string) $fields[4]->slug,
        ];

        // these should be encoded
        $encoded = [
            (string) $fields[1]->slug,
            (string) $fields[2]->slug,
            (string) $fields[4]->slug,
            (string) $fields[5]->slug,
        ];

        // these is what we want to store
        $values = [
            'unknown-field-slug' => 'invisible -> unknown slug',
            ((string) $fields[0]->slug) => 'visible unprotected',
            ((string) $fields[1]->slug) => 'visible protection evelated',
            ((string) $fields[2]->slug) => 'visible protection maximum',
            ((string) $fields[3]->slug) => 'visible searchable unprotected',
            ((string) $fields[4]->slug) => 'visible searchable protection evelated',
            ((string) $fields[5]->slug) => 'visible searchable protection maximum',
        ];

        $this->encrypter->shouldReceive('encrypt')->with($values[(string) $fields[1]->slug])->andReturn('s:'.$values[(string) $fields[1]->slug]);
        $this->encrypter->shouldReceive('decrypt')->with('s:'.$values[(string) $fields[1]->slug])->andReturn($values[(string) $fields[1]->slug]);
        $this->encrypter->shouldReceive('maximum')->with($values[(string) $fields[2]->slug])->andReturn('m:'.$values[(string) $fields[2]->slug]);
        $this->encrypter->shouldReceive('decrypt')->with('m:'.$values[(string) $fields[2]->slug])->andReturn($values[(string) $fields[2]->slug]);
        $this->encrypter->shouldReceive('encrypt')->with($values[(string) $fields[4]->slug])->andReturn('s:'.$values[(string) $fields[4]->slug]);
        $this->encrypter->shouldReceive('decrypt')->with('s:'.$values[(string) $fields[4]->slug])->andReturn($values[(string) $fields[4]->slug]);
        $this->encrypter->shouldReceive('maximum')->with($values[(string) $fields[5]->slug])->andReturn('m:'.$values[(string) $fields[5]->slug]);
        $this->encrypter->shouldReceive('decrypt')->with('m:'.$values[(string) $fields[5]->slug])->andReturn($values[(string) $fields[5]->slug]);
        // this is where we want to store
        $object = $this->muffin(Entry::class);

        // lets store
        $this->valuesService->setValuesForObject($values, $object);

        // lets retrieve
        $retrievedValues = $this->valuesService->getValuesForObject($object);

        // check if expected visibles are visible
        $this->assertCount(6, $retrievedValues);
        $this->assertCount(
            6,
            collect($retrievedValues)->filter(
                function (string $value) {
                    return substr($value, 0, 7) === 'visible';
                }
            )
        );

        // check if protected were encoded
        $this->assertEquals(
            array_values(array_sort(collect($retrievedValues)->filter(
                function ($retrievedValue, $slug) use ($object) {
                    return $retrievedValue !== $object->values[$slug];
                }
            )->keys()->toArray())),
            array_values(array_sort($encoded))
        );

        // check if searchables have hashes generated
        $this->assertEquals(
            array_values(array_sort($searchables)),
            array_values(array_sort(array_keys($object->hashes)))
        );

        $newField = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'protection' => Field::PROTECTION_STANDARD]);

        $newValues = [
            ((string) $fields[3]->slug) => 'visible updated',
            ((string) $newField->slug) => 'visible new',
        ];

        $expectedValues = [
            ((string) $fields[0]->slug) => 'visible unprotected',
            ((string) $fields[1]->slug) => 'visible protection evelated',
            ((string) $fields[2]->slug) => 'visible protection maximum',
            ((string) $fields[4]->slug) => 'visible searchable protection evelated',
            ((string) $fields[5]->slug) => 'visible searchable protection maximum',
            ((string) $fields[3]->slug) => 'visible updated',
            ((string) $newField->slug) => 'visible new',
        ];

        // lets sync
        $this->valuesService->syncValuesForObject($newValues, $object)->refresh();
        $retrievedValues = $this->valuesService->getValuesForObject($object)->toArray();
        $this->assertCount(7, $retrievedValues);
        $this->assertEquals($expectedValues, $retrievedValues);

        // lets set ( overwrite )
        $this->valuesService->setValuesForObject($newValues, $object)->refresh();
        $retrievedValues = $this->valuesService->getValuesForObject($object)->toArray();
        $this->assertCount(2, $retrievedValues);
        $this->assertEquals($newValues, $retrievedValues);
    }

    public function testIfServiceUpdatesFilesRelations(): void
    {
        $fields = [
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'protection' => Field::PROTECTION_STANDARD, 'type' => function () {
                return Field::TYPE_FILE;
            }]),
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'protection' => Field::PROTECTION_STANDARD, 'type' => function () {
                return Field::TYPE_FILE;
            }]),
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'protection' => Field::PROTECTION_STANDARD, 'type' => function () {
                return Field::TYPE_FILE;
            }]),
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'protection' => Field::PROTECTION_STANDARD, 'type' => function () {
                return Field::TYPE_FILE;
            }]),
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'protection' => Field::PROTECTION_STANDARD]),
        ];

        $files = [
            $this->muffin(File::class, ['resource' => 'File field-'.$fields[0]->id, 'resource_id' => $fields[0]->id, 'token' => 'tokenA']),
            $this->muffin(File::class, ['resource' => 'File field-'.$fields[1]->id, 'resource_id' => $fields[1]->id, 'token' => 'tokenB']),
            $this->muffin(File::class, ['resource' => 'Entries'.$fields[2]->id, 'resource_id' => $fields[2]->id, 'token' => 'tokenC']),
            $this->muffin(File::class, ['resource' => 'File field-'.$fields[3]->id, 'resource_id' => $fields[3]->id, 'token' => 'tokenD']),
            $this->muffin(File::class, ['resource' => 'File field-'.$fields[4]->id, 'resource_id' => $fields[4]->id, 'token' => 'tokenE']),
        ];

        $values = [
            ((string) $fields[0]->slug) => 'tokenA',
            ((string) $fields[1]->slug) => 'unknown-token',
            ((string) $fields[2]->slug) => 'tokenC',
            ((string) $fields[4]->slug) => 'tokenE',
        ];

        $entry = $this->muffin(Entry::class);

        $this->valuesService->setValuesForObject($values, $entry);

        foreach ($files as $file) {
            $file->refresh();
        }

        $this->assertEquals($entry->id, $files[0]->foreignId);
        $this->assertNotEquals($entry->id, $files[1]->foreignId);
        $this->assertNotEquals($entry->id, $files[2]->foreignId);
        $this->assertNotEquals($entry->id, $files[3]->foreignId);
        $this->assertNotEquals($entry->id, $files[4]->foreignId);
    }

    public function testValuesNormaliser(): void
    {
        // get some fields
        $fields = $this->muffins(7, Field::class, ['resource' => Field::RESOURCE_FORMS, 'protection' => Field::PROTECTION_STANDARD]);
        $protected_field = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'protection' => Field::PROTECTION_MAXIMUM]);
        $protected_field_null = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'protection' => Field::PROTECTION_MAXIMUM]);
        $table_field = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'type' => Field::TYPE_TABLE]);

        // these is what we want to store
        $values = [
            ((string) $fields[0]->slug) => 'string',
            ((string) $fields[1]->slug) => ['this', 'is', 'array'],
            ((string) $fields[2]->slug) => ['a' => 'this', 'b' => 'is', 'c' => 'assoc'],
            ((string) $fields[3]->slug) => (object) ['a' => 'this', 'b' => 'is', 'c' => 'object'],
            ((string) $fields[4]->slug) => 123,
            ((string) $fields[5]->slug) => true,
            ((string) $fields[6]->slug) => '{"a": "this", "b": "is", "c": "{\"json\":[],\"string\":{}}"}',
            ((string) $protected_field->slug) => '{"a": "this", "b": "is", "c": "{\"json\":[],\"string\":{}}", "d": "for protected field"}',
            ((string) $table_field->slug) => '{"a": "this", "b": "is", "c": "{\"json\":[],\"string\":{}}", "d": "for table field"}',
        ];

        $this->encrypter->shouldReceive('maximum')->andReturn('m:'.$values[(string) $protected_field->slug]);
        $this->encrypter->shouldReceive('decrypt')->with('m:'.$values[(string) $protected_field->slug])->andReturn($values[(string) $protected_field->slug]);
        // this is where we want to store
        $object = $this->muffin(Entry::class);

        // lets store
        $this->valuesService->setValuesForObject($values, $object);

        // hack in null values into protected fields
        // this should NEVER been done in real life
        // this hack is to test migrations compatibility
        $values = $object->values;
        $values[(string) $protected_field_null->slug] = null;
        $object->values = $values;
        $protected = $object->protected;
        $protected[(string) $protected_field_null->slug] = 1;
        $object->protected = $protected;
        $object->save();
        $object->refresh();

        // lets retrieve
        $retrievedValues = $this->valuesService->getValuesForObject($object);

        $tests = [
            ((string) $fields[0]->slug) => function ($v) {
                return is_string($v);
            },
            ((string) $fields[1]->slug) => function ($v) {
                return is_array($v);
            },
            ((string) $fields[2]->slug) => function ($v) {
                return is_array($v) && array_key_exists('a', $v);
            },
            ((string) $fields[3]->slug) => function ($v) {
                return is_object($v) || is_array($v);
            },
            ((string) $fields[4]->slug) => function ($v) {
                return is_numeric($v) || (is_string($v) && (string) 1 * $v === $v);
            },
            ((string) $fields[5]->slug) => function ($v) {
                return is_bool($v) || (is_numeric($v) && ($v === 0 || $v === 1)) || (is_string($v) && ($v === '0' || $v === '1'));
            },
            ((string) $fields[6]->slug) => function ($v) {
                return is_string($v) && json_decode($v) && json_decode($v) != $v;
            },
            ((string) $protected_field->slug) => function ($v) {
                return is_string($v) && json_decode($v) && json_decode($v) != $v;
            },
            ((string) $table_field->slug) => function ($v) {
                return is_array($v) && $v['d'] === 'for table field';
            },
            ((string) $protected_field_null->slug) => function ($v) {
                return $v === null;
            },
        ];

        // check if retrieved values are of expected type
        $this->assertCount(10, $retrievedValues);
        $this->assertCount(
            9,
            collect($retrievedValues)->filter(
                function ($retrievedValue, $slug) use ($tests) {
                    return $tests[$slug]($retrievedValue);
                }
            )
        );
    }

    public function testApplyValuesSearchToEloquent(): void
    {
        // get some fields
        $fields = [
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'type' => 'content', 'protection' => Field::PROTECTION_STANDARD]),
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'type' => 'table', 'protection' => Field::PROTECTION_ELEVATED]),
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'type' => 'textarea', 'protection' => Field::PROTECTION_STANDARD, 'searchable' => 0]),
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'type' => 'drop-down-list', 'protection' => Field::PROTECTION_STANDARD, 'searchable' => 0]),
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'type' => 'textarea', 'protection' => Field::PROTECTION_ELEVATED, 'searchable' => 1]),
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'type' => 'textarea', 'protection' => Field::PROTECTION_MAXIMUM, 'searchable' => 1]),
        ];

        // prepare slugs
        $slugs = [
            (string) $fields[0]->slug,
            (string) $fields[1]->slug,
            (string) $fields[2]->slug,
            (string) $fields[3]->slug,
            (string) $fields[4]->slug,
            (string) $fields[5]->slug,
            'unknown_slug',
        ];

        // generate expected hashes for elevated fields
        $hashes = [
            $slugs[1] => hash('sha256', $slugs[1].':'.preg_replace('/[\x00-\x1F\x7F\s]/s', '', strtolower('elevated text'))),
            $slugs[4] => hash('sha256', $slugs[4].':'.preg_replace('/[\x00-\x1F\x7F\s]/s', '', strtolower('elevated searchable text'))),
        ];

        // simulate search query
        $searches = [
            $slugs[0] => 'standard text',
            $slugs[1] => 'elevated text',
            $slugs[2] => "standard sEaRcHaBlE text 'with' quotes",
            $slugs[3] => 'standard sEaRcHaBlE drop-down-list',
            $slugs[4] => 'elevated searchable text',
            $slugs[5] => 'maximum (non)searchable text',
            $slugs[6] => 'unknown field value',
        ];

        // mock eloquent builder and set expectations
        $query = m::mock(Builder::class);

        // expect full text search for textarea
        $query->shouldReceive('whereRaw')->with(m::on(
            function ($query) use ($slugs, $searches) {
                return $query->getValue(DB::connection()->getQueryGrammar()) === "LOWER(entries.hashes->'$.".$slugs[2]."') LIKE '%".addslashes(strtolower($searches[$slugs[2]]))."%'";
            }
        ))->once()->andReturnSelf();

        // expect simple compare for dropdown field
        $query->shouldReceive('whereRaw')->with(m::on(
            function ($query) use ($slugs, $searches) {
                return $query->getValue(DB::connection()->getQueryGrammar()) === "LOWER(entries.hashes->'$.".$slugs[3]."') = '\"".strtolower($searches[$slugs[3]])."\"'";
            }
        ))->once()->andReturnSelf();

        // expect hash compare for elevated field
        $query->shouldReceive('where')->with('entries.hashes->'.$slugs[4], $hashes[$slugs[4]])->once()->andReturnSelf();

        // apply values search to eloquent mock
        $this->valuesService->applyValuesSearchToEloquent($query, $searches);
    }

    public function testMapValuesToFieldsForObject(): void
    {
        // these are some fields
        $fields = $this->muffins(4, Field::class, ['resource' => Field::RESOURCE_FORMS]);

        // these are values for the fields
        $values = [];
        foreach ($fields as $field) {
            $values[(string) $field->slug] = 'value '.((string) $field->slug);
        }

        // this is where we want to store
        $object = $this->muffin(Entry::class);

        // lets set values
        $this->valuesService->setValuesForObject($values, $object);

        // and get all of them mapped into fields
        $fieldsWithValues = $this->valuesService->mapValuesToFields($object)->pluck('value', 'slug')->toArray();
        $this->assertEquals($values, $fieldsWithValues);

        // and get some of them mapped into fields
        $fieldsWithValues = $this->valuesService->mapValuesToFields($object, new Fields(array_slice($fields, 1, 2)))->pluck('value', 'slug')->toArray();
        $this->assertEquals(array_slice($values, 1, 2), $fieldsWithValues);
    }

    public function testRecreateValuesForField(): void
    {
        $field = $this->muffin(Field::class, ['protection' => Field::PROTECTION_STANDARD, 'resource' => Field::RESOURCE_FORMS]);

        $value = 'Lorem ipsum dolor sit amet';

        $values = [
            ((string) $field->slug) => $value,
        ];

        $object = $this->muffin(Entry::class);
        $this->valuesService->setValuesForObject($values, $object);

        $retrieved = $this->valuesService->getValuesForObject($object)->toArray();
        $this->assertEquals($value, $object->values[(string) $field->slug]);
        $this->assertEquals($object->protected[(string) $field->slug] ?? 0, 0);
        $this->assertEquals($values, $retrieved);

        $field->protection = Field::PROTECTION_ELEVATED;
        $field->save();
        $this->encrypter->shouldReceive('encrypt')->with($value)->andReturn('s:'.$value);
        $this->encrypter->shouldReceive('decrypt')->with('s:'.$value)->andReturn($value);
        $this->valuesService->recreateValuesForField($field);
        $object->refresh();
        $retrieved = $this->valuesService->getValuesForObject($object)->toArray();
        $this->assertNotEquals($value, $object->values[(string) $field->slug]);
        $this->assertEquals('s:', substr($object->values[(string) $field->slug], 0, 2));
        $this->assertEquals(1, $object->protected[(string) $field->slug]);
        $this->assertEquals($values, $retrieved);
    }

    public function testRecreateValuesForObject(): void
    {
        $field1 = $this->muffin(Field::class, ['protection' => Field::PROTECTION_STANDARD, 'resource' => Field::RESOURCE_FORMS]);
        $field2 = $this->muffin(Field::class, ['protection' => Field::PROTECTION_STANDARD, 'resource' => Field::RESOURCE_FORMS]);

        $values = [
            ((string) $field1->slug) => 'field1',
            ((string) $field2->slug) => 'field2',
        ];

        $object = $this->muffin(Entry::class);
        $this->valuesService->setValuesForObject($values, $object);

        $this->encrypter->shouldReceive('encrypt')->with('field1')->andReturn('s:field1');
        $this->encrypter->shouldReceive('decrypt')->with('s:field1')->andReturn('field1');
        $field1->protection = Field::PROTECTION_ELEVATED;
        $field1->save();

        $this->encrypter->shouldReceive('maximum')->with('field2')->andReturn('m:field2');
        $this->encrypter->shouldReceive('decrypt')->with('m:field2')->andReturn('field2');
        $field2->protection = Field::PROTECTION_MAXIMUM;
        $field2->save();

        $this->valuesService->recalculateValuesForObject($object);
        $object->refresh();

        $this->assertEquals('s:', substr($object->values[(string) $field1->slug], 0, 2));
        $this->assertEquals('m:', substr($object->values[(string) $field2->slug], 0, 2));
    }

    public function testGetMissingUserFieldsForSeasonAndUser(): void
    {
        $seasonId = 100;

        // get some fields
        $fields = [
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_USERS, 'season_id' => $seasonId, 'order' => 1000]),
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_USERS, 'season_id' => $seasonId, 'required' => true, 'order' => 999]),
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_USERS, 'season_id' => $seasonId, 'order' => 998]),
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_USERS, 'season_id' => $seasonId, 'required' => true, 'order' => 997]),
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_USERS, 'season_id' => $seasonId + 1, 'order' => 996]),
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_USERS, 'season_id' => $seasonId + 1, 'required' => true, 'order' => 995]),
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_USERS, 'season_id' => $seasonId + 1, 'order' => 994]),
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_USERS, 'season_id' => $seasonId + 1, 'required' => true, 'order' => 993]),
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'season_id' => $seasonId, 'order' => 992]),
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'season_id' => $seasonId, 'required' => true, 'order' => 991]),
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'season_id' => $seasonId, 'order' => 990]),
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'season_id' => $seasonId, 'required' => true, 'order' => 989]),
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_USERS, 'season_id' => $seasonId, 'order' => 988]),
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_USERS, 'season_id' => $seasonId, 'required' => true, 'order' => 987]),
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_USERS, 'type' => 'checkbox', 'season_id' => $seasonId, 'order' => 986]),
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_USERS, 'type' => 'checkbox', 'season_id' => $seasonId, 'required' => true, 'order' => 985]),
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_USERS, 'season_id' => $seasonId, 'order' => 984]),
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_USERS, 'season_id' => $seasonId, 'required' => true, 'order' => 983]),
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_USERS, 'season_id' => $seasonId, 'order' => 982]),
            $this->muffin(Field::class, ['resource' => Field::RESOURCE_USERS, 'season_id' => $seasonId, 'required' => true, 'order' => 981]),
        ];

        // these is what we want to store
        $values = [
            ((string) $fields[0]->slug) => 'value',
            ((string) $fields[1]->slug) => 'value',
            ((string) $fields[4]->slug) => 'value',
            ((string) $fields[5]->slug) => 'value',
            ((string) $fields[8]->slug) => 'value',
            ((string) $fields[9]->slug) => 'value',
            ((string) $fields[12]->slug) => '',
            ((string) $fields[13]->slug) => '',
            ((string) $fields[14]->slug) => 0,
            ((string) $fields[15]->slug) => 0,
            ((string) $fields[16]->slug) => null,
            ((string) $fields[17]->slug) => null,
            ((string) $fields[18]->slug) => 0,
            ((string) $fields[19]->slug) => 0,
        ];

        $expectedMissingFieldsSlugs = [
            ((string) $fields[2]->slug),
            ((string) $fields[3]->slug),
            ((string) $fields[12]->slug),
            ((string) $fields[13]->slug),
            ((string) $fields[14]->slug),
            ((string) $fields[15]->slug),
            ((string) $fields[16]->slug),
            ((string) $fields[17]->slug),
        ];

        $expectedMissingRequiredFieldsSlugs = [
            ((string) $fields[3]->slug),
            ((string) $fields[13]->slug),
            ((string) $fields[15]->slug),
            ((string) $fields[17]->slug),
        ];

        $user = $this->setupUserWithRole('Entrant');
        $user->registerMembership(current_account(), 'en_GB');
        $membership = $user->currentMembership;
        // lets store
        $this->valuesService->setValuesForObject($values, $membership);

        $missingFields = $this->valuesService->getMissingUserFieldsForSeason($user, $seasonId);
        $this->assertEmpty(
            $missingFields->filter(
                function (Field $field) use ($expectedMissingFieldsSlugs) {
                    return ! in_array((string) $field->slug, $expectedMissingFieldsSlugs);
                }
            )
        );
        $this->assertCount($missingFields->count(), $expectedMissingFieldsSlugs);

        $missingFields = $this->valuesService->getMissingRequiredUserFieldsForSeason($user, $seasonId);
        $this->assertEmpty(
            $missingFields->filter(
                function (Field $field) use ($expectedMissingRequiredFieldsSlugs) {
                    return ! in_array((string) $field->slug, $expectedMissingRequiredFieldsSlugs);
                }
            )
        );
        $this->assertCount($missingFields->count(), $expectedMissingRequiredFieldsSlugs);

        // Orderings
        $missingFields = $missingFields->values();
        for ($i = 0; $i < $missingFields->count() - 1; $i++) {
            $this->assertTrue($missingFields[$i]->order < $missingFields[$i + 1]->order);
        }
    }

    public function testNullifyValuesForFields(): void
    {
        $fields = $this->muffins(3, Field::class, ['resource' => Field::RESOURCE_FORMS]);
        $entries = $this->muffins(3, Entry::class);

        $this->valuesService->setValuesForObject([
            ((string) $fields[0]->slug) => 'entry.0.field.0',
            ((string) $fields[1]->slug) => 'entry.0.field.1',
            ((string) $fields[2]->slug) => 'entry.0.field.2',
        ], $entries[0]);

        $this->valuesService->setValuesForObject([
            ((string) $fields[0]->slug) => 'entry.1.field.0',
            ((string) $fields[2]->slug) => 'entry.1.field.2',
        ], $entries[1]);

        $this->valuesService->setValuesForObject([
            ((string) $fields[0]->slug) => 'entry.2.field.0',
            ((string) $fields[1]->slug) => 'entry.2.field.1',
            ((string) $fields[2]->slug) => 'entry.2.field.2',
        ], $entries[2]);

        $this->valuesService->nullifyValuesForFields(
            [$fields[0]->id, $fields[1]->id],
            [$entries[0]->id, $entries[1]->id]
        );

        $expected = [
            $entries[0]->id => [
                ((string) $fields[0]->slug) => null,
                ((string) $fields[1]->slug) => null,
                ((string) $fields[2]->slug) => 'entry.0.field.2',
            ],
            $entries[1]->id => [
                ((string) $fields[0]->slug) => null,
                ((string) $fields[2]->slug) => 'entry.1.field.2',
            ],
            $entries[2]->id => [
                ((string) $fields[0]->slug) => 'entry.2.field.0',
                ((string) $fields[1]->slug) => 'entry.2.field.1',
                ((string) $fields[2]->slug) => 'entry.2.field.2',
            ],
        ];

        foreach ($entries as $entry) {
            $entry->refresh();
            $this->assertEquals($entry->values, $expected[$entry->id]);
        }
    }

    public function testGetFieldValuesForObjects(): void
    {
        $objects = $this->muffins(5, Entry::class);
        $fields = $this->muffins(3, Field::class, ['resource' => Field::RESOURCE_FORMS]);

        $values = [];
        $objectsIndexes = [];
        foreach ($objects as $key => $object) {
            $objectsIndexes[$object->id] = $key;
            $values[$key] = collect($fields)->mapWithKeys(
                function (Field $field) use ($object) {
                    return [(string) $field->slug => "{$field->slug}-{$object->id}"];
                }
            )->toArray();

            $this->valuesService->setValuesForObject($values[$key], $object)->refresh();
        }

        foreach ($fields as $field) {
            $tested = collect($objects)->random(3);
            $expected = $tested->mapWithKeys(
                function ($object) use ($objectsIndexes, $values, $field) {
                    return [$object->id => $values[$objectsIndexes[$object->id]][(string) $field->slug]];
                }
            );

            $this->assertEquals(
                $expected,
                $this->valuesService->getFieldValuesForObjects($field->id, $tested)
            );
        }
    }

    public function testGetVisibleFieldValuesForObjects(): void
    {
        $categoryA = $this->muffin(Category::class);
        $categoryB = $this->muffin(Category::class);

        $objects = collect([
            $entryA = $this->muffin(Entry::class, ['category_id' => $categoryA->id]),
            $entryB = $this->muffin(Entry::class, ['category_id' => $categoryB->id]),
        ]);

        $fields = new Fields([
            $fieldCategoryA1 = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'category_option' => function () {
                return Field::CATEGORY_OPTION_SELECT;
            }]),
            $fieldCategoryA2 = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'category_option' => function () {
                return Field::CATEGORY_OPTION_SELECT;
            }]),
            $fieldCategoryB = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'category_option' => function () {
                return Field::CATEGORY_OPTION_SELECT;
            }]),
            $fieldAllCategories1 = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'category_option' => function () {
                return Field::CATEGORY_OPTION_ALL;
            }]),
            $fieldAllCategories2 = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'category_option' => function () {
                return Field::CATEGORY_OPTION_SELECT;
            }]),
        ]);

        $this->fields->syncCategories($fieldCategoryA1, [$categoryA->id]);
        $this->fields->syncCategories($fieldCategoryA2, [$categoryA->id]);
        $this->fields->syncCategories($fieldCategoryB, [$categoryB->id]);
        $this->fields->syncCategories($fieldAllCategories2, [$categoryA->id, $categoryB->id]);

        $this->valuesService->setValuesForObject(
            $values = [
                (string) $fieldCategoryA1->slug => 'value A1',
                (string) $fieldCategoryA2->slug => 'value A2',
                (string) $fieldCategoryB->slug => 'value B',
                (string) $fieldAllCategories1->slug => 'value ALL 1',
                (string) $fieldAllCategories2->slug => 'value ALL 2',
            ],
            $entryA
        );
        $this->valuesService->setValuesForObject($values, $entryB);

        $this->assertEquals(
            [
                (string) $fieldCategoryA1->slug => 'value A1',
                (string) $fieldCategoryA2->slug => 'value A2',
                (string) $fieldAllCategories1->slug => 'value ALL 1',
                (string) $fieldAllCategories2->slug => 'value ALL 2',
            ],
            $this->valuesService->loadVisibleEntryFieldValues($objects, $fields)->get($entryA->id)
        );

        $this->assertEquals(
            [
                (string) $fieldCategoryB->slug => 'value B',
                (string) $fieldAllCategories1->slug => 'value ALL 1',
                (string) $fieldAllCategories2->slug => 'value ALL 2',
            ],
            $this->valuesService->loadVisibleEntryFieldValues($objects, $fields)->get($entryB->id)
        );
    }

    public function testGetObjectByFieldAndForeign(): void
    {
        $fields = $this->muffins(2, Field::class, ['resource' => Field::RESOURCE_FORMS]);
        $entries = $this->muffins(2, Entry::class);

        $this->valuesService->setValuesForObject([
            (string) $fields[0]->slug => 'field.0.entry.0',
        ], $entries[0])->refresh();

        $this->valuesService->setValuesForObject([
            (string) $fields[0]->slug => 'field.0.entry.1',
            (string) $fields[1]->slug => 'field.1.entry.1',
        ], $entries[1])->refresh();

        $this->assertEquals($entries[0]->id, $this->valuesService->getObjectByFieldAndForeign($fields[0]->id, $entries[0]->id)->id);
        $this->assertEquals($entries[1]->id, $this->valuesService->getObjectByFieldAndForeign($fields[0]->id, $entries[1]->id)->id);
        $this->assertEquals($entries[1]->id, $this->valuesService->getObjectByFieldAndForeign($fields[1]->id, $entries[1]->id)->id);

        // no object for this field
        $this->assertNull($this->valuesService->getObjectByFieldAndForeign($fields[1]->id, $entries[0]->id));

        // no object for not existing field
        $this->assertNull($this->valuesService->getObjectByFieldAndForeign(PHP_INT_MAX, $entries[0]->id));

        // no object for not existing foreign
        $this->assertNull($this->valuesService->getObjectByFieldAndForeign($fields[1]->id, PHP_INT_MAX));
    }

    public function testItSavesUniqueOnly(): void
    {
        app()->instance(EntryRepository::class, $repository = m::mock(EloquentEntryRepository::class));
        $valuesService = app(ValuesService::class);

        $repository->shouldReceive('calculateLocalId')->andReturn(1);

        $field1 = $this->addField('Entries');
        $field2 = $this->addField('Entries');
        $field3 = $this->addField('Entries');
        $field4 = $this->addField('Entries');

        $entry = $this->muffin(Entry::class);

        // it saves new values
        $values = [
            (string) $field1->slug => '1',
            (string) $field2->slug => '2',
        ];
        $repository->shouldReceive('save')->once();
        $repository->shouldReceive('loadValuesForUpdate')->once()->andReturn($values);
        $valuesService->setValuesForObject($values, $entry);

        // it does not save the same values again
        $values = [
            (string) $field1->slug => '1',
            (string) $field2->slug => '2',
        ];
        $repository->shouldNotReceive('save');
        $repository->shouldReceive('loadValuesForUpdate')->once()->andReturn($values);
        $valuesService->setValuesForObject($values, $entry);

        // it doesn't save reordered fields
        $values = [
            (string) $field2->slug => '2',
            (string) $field1->slug => '1',
        ];
        $repository->shouldNotReceive('save');
        $repository->shouldReceive('loadValuesForUpdate')->once()->andReturn($values);
        $valuesService->setValuesForObject($values, $entry);

        // it saves changed values
        $values = [
            (string) $field1->slug => '1',
            (string) $field2->slug => '3',
        ];
        $repository->shouldReceive('save')->once();
        $repository->shouldReceive('loadValuesForUpdate')->once()->andReturn($values);
        $valuesService->setValuesForObject($values, $entry);

        // it saves new values
        $values = [
            (string) $field1->slug => '1',
            (string) $field2->slug => '3',
            (string) $field3->slug => '4',
        ];
        $repository->shouldReceive('save')->once();
        $repository->shouldReceive('loadValuesForUpdate')->once()->andReturn($values);
        $valuesService->setValuesForObject($values, $entry);

        // it saves changed keys
        $values = [
            (string) $field1->slug => '1',
            (string) $field2->slug => '3',
            (string) $field4->slug => '4',
        ];
        $repository->shouldReceive('save')->once();
        $repository->shouldReceive('loadValuesForUpdate')->once()->andReturn($values);
        $valuesService->setValuesForObject($values, $entry);
    }

    public function testHazardousSyncs(): void
    {
        $fieldA = $this->muffin(Field::class);
        $fieldB = $this->muffin(Field::class);
        $entry = $this->muffin(Entry::class);

        // set some entry values
        $this->valuesService->setValuesForObject([
            (string) $fieldA->slug => 'fieldA-1',
            (string) $fieldB->slug => 'fieldB-1',
        ], $entry);

        // introduce a hazard - if for example entity was re-loaded without selecting values column
        // this should never be done intentionally in real code
        $entry->values = null;

        // sync new values in
        $this->valuesService->syncValuesForObject([
            (string) $fieldA->slug => 'fieldA-2',
        ], $entry);

        $entryValues = $entry->fieldValues;
        $expectedValues = [
            (string) $fieldA->slug => 'fieldA-2',
            (string) $fieldB->slug => 'fieldB-1',
        ];

        asort($entryValues);
        asort($expectedValues);

        // check data integrity
        $this->assertSame(
            $expectedValues,
            $entryValues
        );
    }

    public function testHazardousSets(): void
    {
        $fieldA = $this->muffin(Field::class);
        $fieldB = $this->muffin(Field::class);

        $entry = $this->muffin(Entry::class);

        // set some entry values
        $this->valuesService->setValuesForObject([
            (string) $fieldA->slug => 'fieldA-stateA',
            (string) $fieldB->slug => 'fieldB-stateA',
        ], $entry);

        $oldValues = $entry->values;

        // set some new entry values
        $this->valuesService->setValuesForObject([
            (string) $fieldA->slug => 'fieldA-stateB',
            (string) $fieldB->slug => 'fieldB-stateB',
        ], $entry);

        // simulate values change in other session
        $entry->values = $oldValues;
        asort($oldValues);

        $entryValues = $this->entries->getById($entry->id)->fieldValues;
        asort($entryValues);

        // make sure stored values do not match those from 2'nd session
        $this->assertNotSame(
            $entryValues,
            $oldValues
        );

        // revert entry values to stateA
        $this->valuesService->setValuesForObject([
            (string) $fieldA->slug => 'fieldA-stateA',
            (string) $fieldB->slug => 'fieldB-stateA',
        ], $entry);

        $entryValues = $this->entries->getById($entry->id)->fieldValues;
        $expectedValues = [
            (string) $fieldA->slug => 'fieldA-stateA',
            (string) $fieldB->slug => 'fieldB-stateA',
        ];

        asort($entryValues);
        asort($expectedValues);

        // check data integrity
        $this->assertSame(
            $expectedValues,
            $entryValues
        );
    }

    public function testObjectComparer(): void
    {
        $this->assertTrue($this->compareObjects(
            [1, 2, 3],
            [1, 2, 3]
        ));

        $this->assertFalse($this->compareObjects(
            [1, 2, 3],
            [3, 2, 1]
        ));

        $this->assertFalse($this->compareObjects(
            [1, 2, 3],
            [1, 2, 1]
        ));

        $this->assertTrue($this->compareObjects(
            ['a' => 1, 'b' => 2, 'c' => 3],
            ['a' => 1, 'b' => 2, 'c' => 3]
        ));

        $this->assertFalse($this->compareObjects(
            ['a' => 1, 'b' => 2, 'c' => 3],
            ['a' => 1, 'b' => 4, 'c' => 3]
        ));

        $this->assertTrue($this->compareObjects(
            ['a' => 1, 'b' => 2, 'c' => 3],
            ['b' => 2, 'c' => 3, 'a' => 1]
        ));

        $this->assertFalse($this->compareObjects(
            ['a' => 1, 'b' => 2, 'c' => 3],
            ['a' => 1, 'b' => 3, 'c' => 3, '*']
        ));

        $this->assertFalse($this->compareObjects(
            ['a' => 1, 'b' => 2, 'c' => 3],
            null
        ));

        $this->assertTrue($this->compareObjects(
            null,
            null
        ));
    }

    private function compareObjects($a, $b): bool
    {
        return $this->invokePrivate(
            $this->valuesService,
            'compareObjects',
            [$a, $b]
        );
    }

    public function testDoesValueCount(): void
    {
        $checkbox = $this->muffin(Field::class, ['type' => 'checkbox']);
        $text = $this->muffin(Field::class);

        $this->assertFalse($this->doesValueCount($checkbox, 0));
        $this->assertFalse($this->doesValueCount($checkbox, null));
        $this->assertFalse($this->doesValueCount($checkbox, ''));
        $this->assertTrue($this->doesValueCount($checkbox, 1));

        $this->assertTrue($this->doesValueCount($text, 0));
        $this->assertFalse($this->doesValueCount($text, null));
        $this->assertFalse($this->doesValueCount($text, ''));
        $this->assertTrue($this->doesValueCount($text, 1));
    }

    private function doesValueCount($field, $value): bool
    {
        return $this->invokePrivate(
            $this->valuesService,
            'doesValueCount',
            [$field, $value]
        );
    }

    public function testGetFieldsChanges(): void
    {
        $fields = new Fields([
            $non_existing = $this->muffin(Field::class),

            $new_text = $this->muffin(Field::class),
            $new_text_ignored = $this->muffin(Field::class),
            $new_checkbox = $this->muffin(Field::class, ['type' => 'checkbox']),
            $new_checkbox_ignored = $this->muffin(Field::class, ['type' => 'checkbox']),

            $old_text = $this->muffin(Field::class),
            $old_text_ignored = $this->muffin(Field::class),
            $old_checkbox = $this->muffin(Field::class, ['type' => 'checkbox']),
            $old_checkbox_ignored = $this->muffin(Field::class, ['type' => 'checkbox']),

            $text = $this->muffin(Field::class),
            $text_ignored = $this->muffin(Field::class),
            $checkbox = $this->muffin(Field::class, ['type' => 'checkbox']),
            $checkbox_ignored = $this->muffin(Field::class, ['type' => 'checkbox']),
        ]);

        $oldValues = [
            (string) $old_text->slug => '*',
            (string) $old_text_ignored->slug => '',
            (string) $old_checkbox->slug => 1,
            (string) $old_checkbox_ignored->slug => 0,

            (string) $text->slug => '*',
            (string) $text_ignored->slug => '',
            (string) $checkbox->slug => 1,
            (string) $checkbox_ignored->slug => 0,
        ];

        $newValues = [
            (string) $new_text->slug => '*',
            (string) $new_text_ignored->slug => '',
            (string) $new_checkbox->slug => 1,
            (string) $new_checkbox_ignored->slug => 0,

            (string) $text->slug => '*',
            (string) $text_ignored->slug => '',
            (string) $checkbox->slug => 1,
            (string) $checkbox_ignored->slug => 0,
        ];

        $fieldsWithNewValues = $this->getAddedFields($oldValues, $newValues, $fields);

        $this->assertCount(2, $fieldsWithNewValues);

        $this->assertTrue($fieldsWithNewValues->contains($new_text));
        $this->assertTrue($fieldsWithNewValues->contains($new_checkbox));
    }

    private function getAddedFields($oldValues, $newValues, $fields)
    {
        return $this->invokePrivate(
            $this->valuesService,
            'addedFields',
            [$oldValues, $newValues, $fields]
        );
    }

    public function testUpdateFilesForValues(): void
    {
        app()->instance(FileRepository::class, $repository = m::mock(EloquentFileRepository::class));
        $valuesService = app(ValuesService::class);

        $fields = new Fields([
            $field1 = $this->muffin(Field::class),
            $field2 = $this->muffin(Field::class, ['type' => function () {
                return Field::TYPE_FILE;
            }]),
            $this->muffin(Field::class, ['type' => function () {
                return Field::TYPE_FILE;
            }]),
        ]);

        $entry = $this->muffin(Entry::class);

        $values = [
            (string) $field1->slug => 'token1',
            (string) $field2->slug => 'token2',
        ];

        $repository->shouldReceive('updateFileRelationsForToken')->once()->with(
            'token2',
            $field2->id,
            $entry->id
        );

        $this->invokePrivate(
            $valuesService,
            'updateFilesForValues',
            [$fields, $entry, $values]
        );
    }

    public function testNormaliseValue(): void
    {
        $table = $this->muffin(Field::class, ['type' => function () {
            return Field::TYPE_TABLE;
        }]);
        $text = $this->muffin(Field::class);

        $var = [1, 2, 3, 4];
        $this->assertSame(json_encode($var), $this->normaliseValue($text, $var));
        $this->assertSame(json_encode($var), $this->normaliseValue($table, $var));

        $var = (object) ['a' => 1, 'b' => 'c'];
        $this->assertSame(json_encode($var), $this->normaliseValue($text, $var));
        $this->assertSame(json_encode($var), $this->normaliseValue($table, $var));

        $values = [
            'aaaaa',
            123,
            -123,
            0,
            true,
            false,
        ];
        foreach ($values as $value) {
            $this->assertSame((string) $value, $this->normaliseValue($text, $value));
            $this->assertSame((string) $value, $this->normaliseValue($table, $value));
        }

        $json = json_encode((object) ['a' => 1, 'b' => 'c']);
        $this->assertSame($json, $this->normaliseValue($table, $json));
        $this->assertSame(json_encode($json), $this->normaliseValue($text, $json));
    }

    public function testNormaliseValueWithInsecureMarkdownLink(): void
    {
        $textarea = $this->muffin(Field::class, ['type' => 'textarea']);
        $text = $this->muffin(Field::class);

        $stringsShouldNotBeModified = [
            'Any string without an URL',
            'Any string with an secure URL https://www.example.com',
            'Any string with http word',
            'Any string with an insecure URL http://www.example.com',
            'A markdown string with an insecure URL [link](https://www.example.com)',
        ];

        $stringsShouldBeModified = [
            'A markdown string with an insecure URL [link](http://www.example.com)' => 'A markdown string with an insecure URL [link](https://www.example.com)',
            'Multiple white space between [link]      (http://www.example.com)' => 'Multiple white space between [link](https://www.example.com)',
        ];

        foreach ($stringsShouldNotBeModified as $string) {
            $this->assertSame($string, $this->normaliseValue($textarea, $string));
            $this->assertSame($string, $this->normaliseValue($text, $string));
        }

        foreach ($stringsShouldBeModified as $string => $expected) {
            $this->assertSame($expected, $this->normaliseValue($textarea, $string));
            $this->assertSame($expected, $this->normaliseValue($text, $string));
        }
    }

    public function testNormaliseValueNormalisesZuluDates(): void
    {
        $dateField = $this->muffin(Field::class, ['type' => fn() => Field::TYPE_DATE]);
        $dateTimeField = $this->muffin(Field::class, ['type' => fn() => Field::TYPE_DATETIME]);

        $this->assertSame('2024-02-29', $this->extractNormalisedRawValue($dateField, '2024-02-29T00:00:00Z'));
        $this->assertSame('2024-02-29 10:30', $this->extractNormalisedRawValue($dateTimeField, '2024-02-29T10:30:00Z'));
    }

    public function testFieldsWithNullValues(): void
    {
        $checkboxListField = $this->muffin(Field::class, ['type' => fn() => Field::TYPE_CHECKBOXLIST]);
        $currencyField = $this->muffin(Field::class, ['type' => fn() => Field::TYPE_CURRENCY]);
        $dateField = $this->muffin(Field::class, ['type' => fn() => Field::TYPE_DATE]);
        $dateTimeField = $this->muffin(Field::class, ['type' => fn() => Field::TYPE_DATETIME]);
        $timeField = $this->muffin(Field::class, ['type' => fn() => Field::TYPE_TIME]);

        $this->assertNull($this->invokePrivate($this->valuesService, 'normaliseValue', [$checkboxListField, null]));
        $this->assertNull($this->invokePrivate($this->valuesService, 'normaliseValue', [$currencyField, null]));
        $this->assertNull($this->invokePrivate($this->valuesService, 'normaliseValue', [$dateField, null]));
        $this->assertNull($this->invokePrivate($this->valuesService, 'normaliseValue', [$dateTimeField, null]));
        $this->assertNull($this->invokePrivate($this->valuesService, 'normaliseValue', [$timeField, null]));
    }

    protected function extractNormalisedRawValue($field, $rawValue): string
    {
        return json_decode(
            $this->normaliseValue($field, [
                $field->type => $rawValue,
            ]),
            true,
        )[$field->type];
    }

    public function testNormalizesMappedValues(): void
    {
        $fields = $this->muffins(2, Field::class, ['resource' => Field::RESOURCE_FORMS]);

        $values = [
            (string) $fields[0]->slug => 'true',
            (string) $fields[1]->slug => 'false',
        ];

        $object = $this->muffin(Entry::class);

        $this->valuesService->setValuesForObject($values, $object);

        $fieldsWithValues = $this->valuesService->mapValuesToFields($object)->pluck('value', 'slug')->toArray();

        $this->assertEquals($values, $fieldsWithValues);
    }

    private function normaliseValue($field, $value)
    {
        return $this->invokePrivate(
            $this->valuesService,
            'normaliseValue',
            [$field, $value]
        );
    }

    public function testSaveObjectViaRepository(): void
    {
        $entry = $this->muffin(Entry::class);
        $startTime = $entry->updatedAt->timestamp;

        Carbon::setTestNow(now()->addSeconds(5));
        $this->saveObjectViaRepository($entry, false);
        $entry->refresh();
        $this->assertEquals($startTime, $entry->updatedAt->timestamp);

        Carbon::setTestNow(now()->addSeconds(5));
        $this->saveObjectViaRepository($entry, true);
        $entry->refresh();
        $this->assertNotEquals($startTime, $entry->updatedAt->timestamp);

        Carbon::setTestNow(now());
    }

    public function testDispatchesEventWhileUpdatingValues(): void
    {
        $entries = $this->muffins(2, Entry::class);
        $fields = $this->muffins(2, Field::class, ['resource' => Field::RESOURCE_FORMS]);

        $values = [
            (string) $fields[0]->slug => 'true',
            (string) $fields[1]->slug => 'false',
        ];
        \Event::fake();

        $this->valuesService->syncValuesForObject($values, $entries[0]);
        $this->valuesService->setValuesForObject($values, $entries[1]);

        \Event::assertDispatchedTimes(UpdatingFieldValuesBeforeEncoding::class, 2);
        \Event::assertDispatchedTimes(UpdatingFieldValuesAfterEncoding::class, 2);
    }

    public function testApplyValuesSearchToEloquentWithHtmlEntities(): void
    {
        $field = $this->muffin(Field::class, ['type' => 'text', 'resource' => Field::RESOURCE_FORMS]);
        $entry = $this->muffin(Entry::class);

        $values = [
            (string) $field->slug => 'test test',
        ];

        $this->valuesService->setValuesForObject($values, $entry);

        $query = Entry::query();
        $this->valuesService->applyValuesSearchToEloquent($query, [(string) $field->slug => 'test&nbsp;test']);

        $this->assertEquals(1, $query->count());
    }

    public function testCanSaveIndividualValues(): void
    {
        $field = $this->muffin(Field::class);

        $entryNullValue = $this->muffin(Entry::class);
        $entryEmptyValue = $this->muffin(Entry::class, ['values' => []]);
        $entryExistingValue = $this->muffin(Entry::class, ['values' => [(string) $field->slug => 'some value']]);

        $entryNullValue = $this->valuesService->syncIndividualFieldValueForObject($entryNullValue, $field, $newValue = 'new value');
        $entryEmptyValue = $this->valuesService->syncIndividualFieldValueForObject($entryEmptyValue, $field, $newValue);
        $entryExistingValue = $this->valuesService->syncIndividualFieldValueForObject($entryExistingValue, $field, $newValue);

        $this->assertEquals($newValue, $entryNullValue->values[(string) $field->slug]);
        $this->assertEquals($newValue, $entryEmptyValue->values[(string) $field->slug]);
        $this->assertEquals($newValue, $entryExistingValue->values[(string) $field->slug]);
    }

    public function testIndividualValueSaveRespectsFieldProtection(): void
    {
        $field = $this->muffin(Field::class, ['protection' => Field::PROTECTION_ELEVATED]);
        $entry = $this->muffin(Entry::class);

        app(Encrypter::class)->shouldReceive('encrypt')
            ->once()
            ->with($value = 'some value')
            ->andReturn($encrypted = 'encrypted data');

        $entry = $this->valuesService->syncIndividualFieldValueForObject($entry, $field, $value);

        $this->assertEquals($encrypted, $entry->values[(string) $field->slug]);
        $this->assertMatchesRegularExpression('/^[a-f0-9]{64}$/', $entry->hashes[(string) $field->slug]);
        $this->assertTrue((bool) $entry->protected[(string) $field->slug]);
    }

    private function saveObjectViaRepository($object, $timestamped)
    {
        return $this->invokePrivate(
            $this->valuesService,
            'saveObjectViaRepository',
            [$object, $timestamped]
        );
    }

    public function testAllowsFormulaFieldValuesWithoutWriteAccessForEntrants(): void
    {
        $form = $this->muffin(Form::class);
        $category = $this->muffin(Category::class, ['form_id' => $form->id]);
        $formulaField = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'type' => 'formula', 'protection' => Field::PROTECTION_STANDARD, 'form_id' => $form->id, 'entrant_read_access' => 1, 'entrant_write_access' => 0]);
        $this->muffin(Entry::class, ['values' => [], 'hashes' => [], 'protected' => [], 'form_id' => $form->id, 'category_id' => $category->id]);

        $values = [
            (string) $formulaField->slug => '123',
        ];

        $filtered = $this->valuesService->filterEntrantsValuesOnlyForFormAndCategory($values, $form->id, $category->id);

        $this->assertArrayHasKey((string) $formulaField->slug, $filtered);
    }
}
