<?php

use AwardForce\Modules\Entries\Models\Contributor;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use AwardForce\Modules\Forms\Fields\Services\VisibleFields\Manager;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use AwardForce\Modules\Referees\Models\Referee;
use Platform\Search\HasValues;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class ManagerTest extends BaseTestCase
{
    use Database;
    use Laravel;

    private ValuesService $values;

    public function init()
    {
        Field::unguard();

        $this->values = app(ValuesService::class);
    }

    public function testForContributors()
    {
        $entry = $this->muffin(Entry::class);
        $contributor = $this->muffin(Contributor::class, ['submittable_id' => $entry->id]);
        [$visibleFields, $hiddenFields] = $this->makeFields($contributor, Field::RESOURCE_CONTRIBUTORS);

        $contributorsFields = app(Manager::class)->forContributors($entry);

        $this->assertCount(1, $contributorsFields);
        $this->assertCount(count($visibleFields), $contributorsFields->first()->fields);
        $this->assertEqualsCanonicalizing(collect($visibleFields)->pluck('id')->toArray(), $contributorsFields->first()->fields->pluck('id')->toArray());
    }

    public function testForReferees()
    {
        $entry = $this->muffin(Entry::class);
        $contributor = $this->muffin(Contributor::class, ['submittable_id' => $entry->id]);
        [$visibleContributorsFields, $hiddenContributorsFields] = $this->makeFields($contributor, Field::RESOURCE_CONTRIBUTORS);
        $referee = $this->muffin(Referee::class, ['submittable_id' => $entry->id]);
        [$visibleRefereesFields, $hiddenRefereesFields] = $this->makeFields($referee, Field::RESOURCE_REFEREES);

        $refereeFields = app(Manager::class)->forReferees($entry);

        $this->assertCount(1, $refereeFields);
        $this->assertCount(count($visibleRefereesFields), $refereeFields->first()->fields);
        $this->assertNotEqualsCanonicalizing(collect($visibleContributorsFields)->pluck('id')->toArray(), $refereeFields->first()->fields->pluck('id')->toArray());
        $this->assertEqualsCanonicalizing(collect($visibleRefereesFields)->pluck('id')->toArray(), $refereeFields->first()->fields->pluck('id')->toArray());
    }

    protected function makeFields(HasValues $object, string $resource): array
    {
        $tab = $this->muffin(Tab::class);
        $hiddenTab = $this->muffin(Tab::class, ['visibleToEntrants' => 0]);
        $fields = $this->muffins(8, Field::class, ['resource' => $resource, 'tab_id' => $tab->id]);

        $visibleField = $this->muffin(Field::class, ['resource' => $resource, 'tab_id' => $tab->id, 'entrant_read_access' => 0]);
        $visibleConditionalField = $this->muffin(Field::class, ['resource' => $resource, 'tab_id' => $tab->id, 'entrant_read_access' => 1]);
        $visibleConditionalField->setConditionalField(1, 'show', $fields[0]->id, 'is', 'hidden field');
        $visibleHiddenConditionalField = $this->muffin(Field::class, ['resource' => $resource, 'tab_id' => $tab->id, 'entrant_read_access' => 1]);
        $visibleHiddenConditionalField->setConditionalField(1, 'hide', $fields[0]->id, 'is', 'hidden field');

        $visibleFieldHiddentab = $this->muffin(Field::class, ['resource' => $resource, 'tab_id' => $hiddenTab->id]);
        $hiddenNoContentField = $this->muffin(Field::class, ['resource' => $resource, 'tab_id' => $tab->id]);

        $this->values->setValuesForObject([
            ((string) $visibleField->slug) => 'hidden field',
            ((string) $visibleConditionalField->slug) => 'shown, conditional on [0] value = hidden field',
            ((string) $visibleHiddenConditionalField->slug) => 'hidden, conditional on [0] value = hidden field',
            ((string) $visibleFieldHiddentab->slug) => 'hidden tab field 1',
            ((string) $hiddenNoContentField->slug) => '',
        ], $object);

        return [[$visibleField, $visibleConditionalField, $visibleFieldHiddentab, $visibleHiddenConditionalField], [$hiddenNoContentField]];
    }

    public function testGetForSubmittableShouldReturnVisibleToEntrantsTabAttribute()
    {
        $entry = $this->muffin(Entry::class);
        [$visibleFields, $hiddenFields] = $this->makeFields($entry, Field::RESOURCE_FORMS);

        $fields = app(Manager::class)->getForSubmittableAllowEmpty($entry, true);

        foreach ($visibleFields as $visibleField) {
            $this->assertNotNull($managerField = $fields->where('id', $visibleField->id)->first());
            $this->assertEquals($visibleField->tab->visibleToEntrants, $managerField->tab->visibleToEntrants);
        }
    }
}
