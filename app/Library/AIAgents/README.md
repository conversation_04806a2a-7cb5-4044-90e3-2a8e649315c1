# AI Agents Library

A collection of tools for working with AI services in The Force.

## Purpose

This library is a simple wrapper around third-party AI packages (right now we're using Prism under the hood). The idea is to not have direct dependencies on external packages in our application code.

## Usage

Here's how you can generate text with AI models through our simple interface:

### Using Dependency Injection

```php
use AwardForce\Library\AIAgents\Contracts\TextGenerator;

class MyService
{
    public function __construct(private readonly TextGenerator $textGenerator) {}

    public function generateText(): string
    {
        $response = $this->textGenerator->prompt(Model::<PERSON><PERSON><PERSON><PERSON><PERSON>, 'Tell me a short story about a brave knight.');

        return $response->text;
    }
}
```

## Response Object

The `Response` object provides access to the generated text and usage information:

```php
$response = $this->textGenerator->prompt(Model::Claude37Sonnet, 'Tell me about quantum computing.');

// Access properties directly
$text = $response->text;
$usage = $response->usage; // Usage object

// Access usage properties
$promptTokens = $usage->promptTokens;
$completionTokens = $usage->completionTokens;
```
