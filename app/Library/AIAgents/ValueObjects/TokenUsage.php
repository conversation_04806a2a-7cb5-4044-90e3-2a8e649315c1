<?php

namespace AwardForce\Library\AIAgents\ValueObjects;

use Illuminate\Contracts\Support\Arrayable;

readonly class TokenUsage implements Arrayable
{
    public function __construct(
        public int $promptTokens,
        public int $completionTokens,
    ) {
    }

    public function toArray(): array
    {
        return [
            'promptTokens' => $this->promptTokens,
            'completionTokens' => $this->completionTokens,
        ];
    }
}
