<?php

use AwardForce\Http\Middleware\RequireAccount;
use AwardForce\Http\Middleware\RequireMembership;
use AwardForce\Http\Middleware\XUACompatible;
use AwardForce\Library\Values\Services\ValueTransformer;
use AwardForce\Modules\Identity\Users\Middleware\BlockUser;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Str;
use Tectonic\LaravelLocalisation\Facades\Translator;

/**
 * A macro to render the SQL from a query builder, including any bindings.
 *
 * @return string
 */
Builder::macro('toSqlWithBindings', function () {
    $bindings = array_map(
        fn($value) => is_numeric($value) ? $value : "'{$value}'",
        $this->getBindings()
    );

    return Str::replaceArray('?', $bindings, $this->toSql());
});

Collection::macro('translate', fn(?string $language = null) => Translator::translate($this, $language));

Collection::macro('shallow', fn(?string $language = null) => Translator::shallow($this, $language));

Collection::macro('mergeIfNotExists', function (...$collections) {
    $merged = new static($this->items);

    foreach ($collections as $collection) {
        foreach ($this->getArrayableItems($collection) as $key => $value) {
            $merged->has($key) ?: $merged->put($key, $value);
        }
    }

    return $merged;
});

Route::macro('web', function (array $except = []) {
    return Route::middleware(array_diff([
        'web',
        'account',
        RequireAccount::class,
        'consumer',
        RequireMembership::class,
        BlockUser::class,
        'pjax',
        'csrf',
        XUACompatible::class,
        'authenticator',
        'preview-mode',
    ], $except));
});

Route::macro('kessel', function (array $except = []) {
    return Route::middleware(array_diff([
        'bindings',
        'kessel',
    ], $except));
});

Request::macro('transformed', function (string $class, string $key, array $default = []) {
    return (new ValueTransformer($class, $this->array($key, $default)))
        ->transform();
});
