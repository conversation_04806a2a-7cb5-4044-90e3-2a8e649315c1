<?php

namespace AwardForce\Http;

use AwardForce\Modules\Authentication\Middleware\ValidateSocialAuthToken;
use AwardForce\Modules\Forms\Collaboration\Middleware\Collaborator;
use AwardForce\Modules\Forms\Fields\Http\Middleware\ResourceFeature;
use AwardForce\Modules\Forms\Forms\Http\Middleware\Owner;
use Illuminate\Foundation\Http\Kernel as HttpKernel;

class Kernel extends HttpKernel
{
    /**
     * The bootstrap classes for the application.
     *
     * @var array
     */
    protected $bootstrappers = [
        \Illuminate\Foundation\Bootstrap\LoadEnvironmentVariables::class,
        \Illuminate\Foundation\Bootstrap\LoadConfiguration::class,
        \AwardForce\Library\Context\Bootstrap::class,
        \Illuminate\Foundation\Bootstrap\HandleExceptions::class,
        \Illuminate\Foundation\Bootstrap\RegisterFacades::class,
        \Illuminate\Foundation\Bootstrap\RegisterProviders::class,
        \Illuminate\Foundation\Bootstrap\BootProviders::class,
    ];

    /**
     * The application's global HTTP middleware stack.
     *
     * @var array
     */
    protected $middleware = [
        \Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode::class,
        \Illuminate\Foundation\Http\Middleware\ValidatePostSize::class,
        \AwardForce\Http\Middleware\TrimStrings::class,
        //\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull::class,  // @TODO: enable after investigating effects
        \AwardForce\Http\Middleware\Finalise::class,
    ];

    /**
     * The application's route middleware groups.
     *
     * @var array
     */
    protected $middlewareGroups = [
        'web' => [
            \Platform\Http\Middleware\RedirectToHttps::class,
            \AwardForce\Http\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \AwardForce\Http\Middleware\AccountMiddleware::class,
            \AwardForce\Modules\Regions\Middleware\RegionalMaintenance::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            \Illuminate\Session\Middleware\AuthenticateSession::class,
            \AwardForce\Http\Middleware\SuspendedAccount::class,
            \AwardForce\Http\Middleware\Finalise::class,
            \AwardForce\Http\Middleware\Breadcrumb::class,
            \AwardForce\Http\Middleware\RequireJson::class,
        ],

        // Identical to web, but we do not want Finalise running!
        'provisioning' => [
            \Platform\Http\Middleware\RedirectToHttps::class,
            \AwardForce\Http\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \AwardForce\Modules\Regions\Middleware\RegionalMaintenance::class,
            \AwardForce\Http\Middleware\DefaultDatabase::class,
            'provisioning.language',
        ],

        'api' => [
            'throttle:60,1',
            'bindings',
        ],

        // Authenticated user with no role or membership expectations
        'auth.user' => [
            \AwardForce\Http\Middleware\Authenticate::class,
            \AwardForce\Modules\Identity\Users\Middleware\ProfileComplete::class,
            \AwardForce\Http\Middleware\TrialEnded::class,
            ValidateSocialAuthToken::class,
        ],

        // Authenticated user with valid role required
        'auth.role' => [
            \AwardForce\Http\Middleware\Authorize::class,
            \AwardForce\Modules\Identity\Users\Middleware\ProfileComplete::class,
            \AwardForce\Http\Middleware\TrialEnded::class,
            ValidateSocialAuthToken::class,
        ],
    ];

    /**
     * The application's route middleware.
     *
     * @var array
     */
    protected $middlewareAliases = [
        'account' => \AwardForce\Http\Middleware\AccountMiddleware::class,
        'agreement' => \AwardForce\Modules\Judging\Middleware\Agreement::class,
        'authenticator' => \AwardForce\Modules\Authentication\Middleware\Authenticator::class,
        'allowedEntries' => \AwardForce\Modules\Judging\Middleware\AllowedEntries::class,
        'api.v2.authenticate' => \AwardForce\Http\Middleware\Api\V2\Authenticate::class,
        'api.v2.authorize' => \AwardForce\Http\Middleware\Api\V2\Authorize::class,
        'api.v2.database' => \AwardForce\Http\Middleware\Api\V2\Database::class,
        'api.v2.entry-attachment-upload-parameters' => \AwardForce\Http\Middleware\Api\V2\EntryAttachmentUploadParameters::class,
        'api.v2.file-field-upload-parameters' => \AwardForce\Http\Middleware\Api\V2\FileFieldUploadParameters::class,
        'api.v2.json-validation' => \AwardForce\Http\Middleware\Api\V2\JsonValidation::class,
        'api.v2.not-empty-body' => \AwardForce\Http\Middleware\Api\V2\EmptyBodyValidation::class,
        'api.v2.slug-parameter' => \AwardForce\Http\Middleware\Api\V2\SlugParameter::class,
        'api.v2.throttle' => \AwardForce\Modules\Api\V2\Middleware\RateLimit::class,
        'api.v2.token-parameter' => \AwardForce\Http\Middleware\Api\V2\TokenParameter::class,
        'api.v2.user-parameter' => \AwardForce\Http\Middleware\Api\V2\UserParameter::class,
        'api.v2.version-bindings' => \AwardForce\Http\Middleware\Api\V2\VersionBindings::class,
        'bindings' => \Illuminate\Routing\Middleware\SubstituteBindings::class,
        'consumer' => \AwardForce\Http\Middleware\Consumer::class,
        'csrf' => \AwardForce\Http\Middleware\VerifyCsrfToken::class,
        'entryLimitReached' => \AwardForce\Modules\Entries\Middleware\EntrantEntryLimitReached::class,
        'feature' => \AwardForce\Http\Middleware\Features::class,
        'freshLogin' => \AwardForce\Http\Middleware\FreshLogin::class,
        'formRestricted' => \AwardForce\Modules\Forms\Forms\Http\Middleware\FormRestricted::class,
        'gallery' => \AwardForce\Modules\Galleries\Middleware\GalleryMiddleware::class,
        'guest' => \AwardForce\Http\Middleware\RedirectIfAuthenticated::class,
        'httpsRedirect' => \Platform\Http\Middleware\RedirectToHttps::class,
        'judgingModeAllowed' => \AwardForce\Modules\Judging\Middleware\JudgingModeAllowed::class,
        'kessel' => \Platform\Kessel\Middleware::class,
        'kessel-account' => \AwardForce\Http\Middleware\KesselAccount::class,
        'kessel-default-database' => \AwardForce\Http\Middleware\DefaultDatabase::class,
        'kessel-from' => \AwardForce\Http\Middleware\KesselFrom::class,
        'kessel-region' => \AwardForce\Http\Middleware\KesselRegion::class,
        'kessel-user' => \AwardForce\Http\Middleware\KesselUser::class,
        'manager' => \AwardForce\Modules\Entries\Middleware\ManagerMiddleware::class,
        'intendedRedirection' => \AwardForce\Http\Middleware\IntendedRedirection::class,
        'isManager' => \AwardForce\Http\Middleware\IsManager::class,
        'isOwner' => \AwardForce\Http\Middleware\IsOwner::class,
        'isOwnerOrProgramManager' => \AwardForce\Http\Middleware\IsOwnerOrProgramManager::class,
        'jedi' => \AwardForce\Http\Middleware\Jedi::class,
        'isNotTrial' => \AwardForce\Http\Middleware\IsNotTrial::class,
        'isTempConsumer' => \AwardForce\Http\Middleware\IsTempConsumer::class,
        'isUserOrTemp' => \AwardForce\Http\Middleware\IsUserOrTemp::class,
        'missingScores' => \AwardForce\Modules\Judging\Middleware\MissingScoresExceptionHandler::class,
        'notAccountOwner' => \AwardForce\Modules\Identity\Users\Middleware\UserIsNotAccountOwner::class,
        'canUpdateUser' => \AwardForce\Modules\Identity\Users\Middleware\UpdateUser::class,
        'outdated' => \AwardForce\Http\Middleware\Outdated::class,
        'useFormSessionUuid' => \AwardForce\Http\Middleware\UseFormSessionUuid::class,
        'pjax' => \Platform\Http\PjaxFilter::class,
        'preview-mode' => \AwardForce\Http\Middleware\PreviewMode::class,
        'preferredLanguage' => \AwardForce\Http\Middleware\PreferredLanguage::class,
        'profileComplete' => \AwardForce\Modules\Identity\Users\Middleware\ProfileComplete::class,
        'remembrance' => \Platform\Http\Middleware\Remembrance::class,
        'roles' => \AwardForce\Http\Middleware\FeatureRoles::class,
        'roleRegistration' => \AwardForce\Modules\Judging\Middleware\RoleRegistration::class,
        'round' => \AwardForce\Modules\Rounds\Middleware\ActiveRound::class,
        'owner' => Owner::class,
        'collaborator' => Collaborator::class,
        'throttle' => \Illuminate\Routing\Middleware\ThrottleRequests::class,
        'trimInput' => \Platform\Http\Middleware\TrimInput::class,
        'contentBlockRoleRestriction' => \AwardForce\Modules\Content\Blocks\Middleware\ContentBlockRoleRestriction::class,
        'tempConsumerWithGlobal' => \AwardForce\Modules\Clear\Middleware\TempConsumerWithGlobal::class,
        'verification' => \AwardForce\Http\Middleware\Verification::class,
        'allowRegistration' => \AwardForce\Http\Middleware\AllowRegistration::class,
        'userLimit' => \AwardForce\Http\Middleware\UserLimit::class,
        'resourceFeature' => ResourceFeature::class,
    ];
}
