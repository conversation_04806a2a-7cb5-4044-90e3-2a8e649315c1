<template>
	<button class="copy-button" @click="copyText">
		<span class="sr-only">Copy text button</span>
		<slot></slot>
	</button>
</template>

<script>
import tectoastr from 'tectoastr';
import { defineComponent } from 'vue';
import { getTrans } from '@/services/global/translations.interface';
import { copyToClipboard } from '@/legacy/MixinProviders';

export default defineComponent({
	props: {
		text: {
			type: String,
			required: true,
		},
	},
	setup(props) {
		const lang = getTrans();

		const copyText = () => {
			copyToClipboard(props.text);

			tectoastr.notify('info', lang.get('miscellaneous.copied_clipboard'));
		};

		return { copyText };
	},
});
</script>

<style scoped>
.copy-button {
	background: none;
	border: none;
	padding: 0;
}
</style>
