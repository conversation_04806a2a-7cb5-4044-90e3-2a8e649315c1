<template>
	<div>
		<div v-if="!formVisible">
			<span v-if="!formVisible" class="username" @mouseover="isHovering = true" @mouseout="isHovering = false">
				<span v-if="currentUser.isDeleted" class="deleted">{{ currentUser.name }}</span>
				<a v-else-if="canUpdateUsers" :href="'/users/' + currentUser.slug">{{ currentUser.name }}</a>
				<span v-else>{{ currentUser.name }}</span>
				<small :class="showEditLink && isHovering ? 'edit-link' : 'edit-link-hide'">
					<a @click.prevent="formVisible = true">{{ label }}</a>
				</small>
			</span>
			{{ currentUser.preferredContact }}
			<copy-text :text="currentUser.preferredContact">
				<i class="af-icons af-icons-clipboard"></i>
			</copy-text>
		</div>
		<form v-else>
			<div class="form-group">
				<search-field
					:initial-id="currentUser.id"
					:initial-value="currentUser.name"
					value-property="name"
					:src="searchUrl"
					:searching-label="searchingLabel"
					@selected="select"
					@error="handleErrors"
				/>
			</div>
			<div class="form-group">
				<button :class="['btn', 'btn-primary', 'btn-sm', { disabled: saving }]" type="submit" @click.prevent="submit">
					{{ saveButtonLabel }}
				</button>
				<button class="btn btn-tertiary btn-sm" @click.prevent="formVisible = false">
					{{ cancelButtonLabel }}
				</button>
			</div>
		</form>
	</div>
</template>

<script>
import { SearchField } from 'vue-bootstrap';
import toastr from 'toastr';
import CopyText from '@/lib/components/Shared/CopyText.vue';

export default {
	components: {
		CopyText,
		SearchField,
	},
	props: {
		user: {
			type: Object,
			required: true,
		},
		canUpdateUsers: {
			type: Boolean,
			default: false,
		},
		label: {
			type: String,
			default: 'Edit user',
		},
		searchingLabel: {
			type: String,
			default: 'Searching...',
		},
		saveButtonLabel: {
			type: String,
			default: 'Save',
		},
		cancelButtonLabel: {
			type: String,
			default: 'Cancel',
		},
		searchUrl: {
			type: String,
			required: true,
		},
		submitUrl: {
			type: String,
			required: true,
		},
		showEditLink: {
			type: Boolean,
			default: true,
		},
		reloadAfterSave: {
			type: Boolean,
			default: false,
		},
		reloadPageUrl: {
			type: String,
			required: false,
			default: null,
		},
		customerId: {
			type: String,
			default: null,
		},
	},
	data() {
		return {
			formVisible: false,
			currentUser: null,
			selectedUser: null,
			saving: false,
			isHovering: false,
		};
	},
	created() {
		this.currentUser = this.user;
	},
	methods: {
		select(user) {
			this.selectedUser = user;
		},
		handleErrors(error) {
			toastr.error(error.response.data.message || document.getElementById('alerts-generic').innerHTML);

			this.saving = false;
			this.$emit('saving', false);
		},
		submit() {
			if (this.saving) return;

			if (!this.selectedUser) {
				this.formVisible = false;
				return;
			}

			const url = this.submitUrl + '/' + this.selectedUser.slug;

			this.saving = true;
			this.$emit('saving', true);

			this.$http.post(url, { customerId: this.customerId }).then(
				() => {
					if (this.reloadAfterSave) {
						this.reloadPage();
					}

					this.currentUser = this.selectedUser;
					this.formVisible = false;

					this.saving = false;
					this.$emit('saving', false);
				},
				(error) => {
					this.handleErrors(error);
				}
			);
		},
		reloadPage() {
			if (this.reloadPageUrl) {
				window.location.href = this.reloadPageUrl;
			} else {
				window.location.reload();
			}
		},
	},
};
</script>

<style scoped>
.edit-link {
	line-height: 19px;
	display: inline-block;
}
.edit-link-hide {
	display: none;
}
</style>
