<template>
	<div>
		<div v-if="showContentBlock" class="col-xs-12 col-md-4 col-md-offset-2">
			<simple-widget>
				<consumer-content-block
					slot="content"
					:content-block="contentBlock"
					:content-block-key="contentBlock.key"
					:editable="consumer.isManager"
					:model-id="contentBlock.id"
					:title-as-link="false"
					class="islet"
					container-class="islet"
					title-tag="h2"
				></consumer-content-block>
				<slot name="social-sharing"></slot>
			</simple-widget>
		</div>

		<div v-if="showForm" :class="formContainerClass">
			<simple-widget widget-class="simple-widget-form">
				<form slot="content" :action="formAction" aria-labelledby="register" class="ignore" novalidate="novalidate">
					<input v-if="defaultRole" :value="defaultRole" name="defaultRole" type="hidden" />
					<input v-if="roleSlug" :value="roleSlug" name="role" type="hidden" />
					<input v-if="invitationToken" :value="invitationToken" name="invitationToken" type="hidden" />
					<quick-register v-if="!newUser">
						<template v-if="!consumer.firstName && !consumer.lastName" #fullName>
							<div>
								<registration-field
									v-model="firstName"
									:label="lang.get('home.register.first_name.label')"
									:required="true"
									name="firstName"
									type="text"
								></registration-field>
								<registration-field
									v-model="lastName"
									:label="lang.get('home.register.last_name.label')"
									:required="true"
									name="lastName"
									type="text"
								></registration-field>
							</div>
						</template>
					</quick-register>
					<div v-else>
						<h2 class="title" v-output="lang.get('home.register.heading')"></h2>
						<language
							v-if="!!invitationToken && supportedLanguages.length > 1"
							:languages="supportedLanguages"
							:preferred-language="preferredLanguage"
						></language>
						<registration-field
							v-model="firstName"
							:label="lang.get('home.register.first_name.label')"
							:required="true"
							name="firstName"
							type="text"
						></registration-field>
						<registration-field
							v-model="lastName"
							:label="lang.get('home.register.last_name.label')"
							:required="true"
							name="lastName"
							type="text"
						></registration-field>
						<div :class="['form-group', { error: !!errors.registerEmail }]">
							<label for="registerEmail">{{ lang.get('home.register.email.label') }}</label>
							<span v-if="emailIsOptional && !consumer.email" class="optional-field">{{
								lang.get('miscellaneous.optional')
							}}</span>
							<registration-email
								v-if="!consumer.email"
								id="registerEmail"
								v-model="registerEmail"
								:required="!emailIsOptional"
								:validate="true"
								name="registerEmail"
							></registration-email>
							<input
								v-else
								id="registerEmail"
								:value="registerEmail"
								class="form-control"
								disabled="disabled"
								name="registerEmail"
								type="email"
							/>
						</div>
						<registration-mobile
							v-if="showRegistrationMobile"
							v-model="registerMobile"
							:disabled="!!consumer.mobile"
						></registration-mobile>
						<registration-password
							v-model="password"
							:required="true"
							name="password"
							type="password"
						></registration-password>
					</div>
					<user-field
						v-for="field in fields"
						:key="field.slug"
						v-model="registrationFieldValues[field.slug]"
						:errors="errors"
						:field="field"
						:required="true"
					></user-field>
					<registration-agreement
						v-for="agreement in existentSettingAgreements"
						:key="agreement.name"
						v-model="agreementValues[agreement.name]"
						:agreement="agreement"
					></registration-agreement>
					<div class="form-group">
						<button
							id="register"
							:disabled="submitInProgress"
							class="btn btn-primary btn-lg lock-on-submit"
							type="submit"
							@click.prevent="submit"
						>
							{{ lang.get('buttons.complete-registration') }}
						</button>
					</div>
				</form>
			</simple-widget>
		</div>
	</div>
</template>

<script>
import RegistrationEmail from '@/lib/components/Authentication/RegistrationEmail.vue';
import PhoneField from '@/lib/components/Fields/PhoneField.vue';
import PasswordField from '@/lib/components/Fields/PasswordField.vue';
import Validator from '@/lib/components/Fields/validator/Validator.vue';
import InstantErrors from '@/lib/components/Fields/validator/InstantErrors.vue';
import RegistrationField from '@/lib/components/Clear/RegistrationField.vue';
import RegistrationMobile from '@/lib/components/Clear/RegistrationMobile.vue';
import RegistrationAgreement from '@/lib/components/Clear/RegistrationAgreement.vue';
import RegistrationPassword from '@/lib/components/Clear/RegistrationPassword.vue';
import UserField from '@/lib/components/Clear/UserField.vue';
import langMixin from '@/lib/components/Translations/mixins/lang-mixin';
import settingsMixin from '@/lib/components/Shared/mixins/settings-mixin';
import QuickRegister from '@/modules/clear/component/QuickRegister.vue';
import { mapGetters, mapMutations, mapState } from 'vuex';
import ContentBlock from '@/lib/components/ContentBlocks/ContentBlock.vue';
import validationErrorsMixin from '@/lib/components/Fields/mixins/validation-errors-mixin';
import SimpleWidget from '@/lib/components/Shared/SimpleWidget.vue';
import ConsumerContentBlock from '@/lib/components/ContentBlocks/ConsumerContentBlock.vue';
import featuresMixin from '@/lib/components/Shared/mixins/features-mixin';
import linksMixin from '@/lib/components/Shared/mixins/links-mixin';
import Language from '@/modules/provisioning/components/Language.vue';

export default {
	components: {
		Language,
		ConsumerContentBlock,
		SimpleWidget,
		ContentBlock,
		QuickRegister,
		RegistrationAgreement,
		RegistrationMobile,
		RegistrationField,
		RegistrationPassword,
		InstantErrors,
		Validator,
		PasswordField,
		PhoneField,
		RegistrationEmail,
		UserField,
	},
	mixins: [langMixin, settingsMixin, validationErrorsMixin, featuresMixin, linksMixin],
	props: {
		formAction: {
			type: String,
			default: '/register',
		},
		defaultRole: {
			type: String,
			default: '',
		},
		roleSlug: {
			type: String,
			default: '',
		},
		invitationToken: {
			type: String,
			default: '',
		},
		agreements: {
			type: Array,
			default: () => [],
		},
		fields: {
			type: Array,
			default: () => [],
		},
		contentBlock: {
			type: Object,
			default: () => {},
		},
		countries: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		const registrationFieldValues = {};
		this.fields.forEach((field) => (registrationFieldValues[field.slug] = ''));
		const agreementValues = {};
		this.agreements.forEach((agreement) => (agreementValues[agreement.name] = agreement.checked));
		return {
			firstName: '',
			lastName: '',
			registerEmail: '',
			registerMobile: '',
			password: '',
			lastNameVal: '',
			registrationFieldValues: registrationFieldValues,
			agreementValues: agreementValues,
			submitInProgress: false,
		};
	},
	computed: {
		...mapGetters('authentication', ['consumer']),
		...mapState('global', ['supportedLanguages', 'preferredLanguage']),
		showForm() {
			return this.settingsService.get('app-site-registration-open') || this.invitationToken;
		},
		newUser() {
			return this.consumer.isNew || this.invitationToken;
		},
		emailIsOptional() {
			return !this.settingsService.enabled('email-required');
		},
		showContentBlock() {
			return this.contentBlock !== undefined && this.contentBlock.id;
		},
		formContainerClass() {
			return 'col-xs-12 col-md-4 ' + (!this.showContentBlock ? 'col-md-offset-4' : '');
		},
		showRegistrationMobile() {
			return (
				(this.settingsService.enabled('enable-mobile-registrations') && !this.consumer.email) ||
				this.settingsService.enabled('mobile-required')
			);
		},
		existentSettingAgreements() {
			return this.agreements.filter((agreement) => this.settingsService.get(agreement.setting));
		},
	},
	created() {
		this.firstName = this.consumer.firstName;
		this.lastName = this.consumer.lastName;
		this.registerEmail = this.consumer.email || '';
		this.registerMobile = this.consumer.mobile ? this.consumer.mobile + '' : '';
		if (Object.values(this.countries).length) {
			this.storeGlobalState({ countries: Object.values(this.countries) });
		}
	},
	methods: {
		...mapMutations('global', ['storeGlobalState']),
		async submit() {
			this.submitInProgress = true;
			await this.$http
				.post(this.formAction, {
					firstName: this.firstName,
					lastName: this.lastName,
					registerEmail: this.registerEmail,
					registerMobile: this.registerMobile,
					password: this.password,
					values: this.registrationFieldValues,
					role: this.roleSlug,
					invitationToken: this.invitationToken,
					...this.agreementValues,
				})
				.catch(() => (this.submitInProgress = false));
		},
	},
};
</script>
