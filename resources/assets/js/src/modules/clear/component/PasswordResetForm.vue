<template>
  <div>
    <div class="row">
      <div class="col-xs-12 col-sm-6 col-sm-offset-3 col-lg-4 col-lg-offset-4">
        <alert-island></alert-island>
        <simple-widget widget-class="simple-widget-form">
          <template slot="title">
            <h1>{{ lang.get('auth.password.reset.title') }}</h1>
          </template>
          <template slot="content">
            <form class="ignore login-form" aria-labelledby="login">
              <div :class="['form-group', 'show_hide_password']">
                <label for="password" style="display: block">
                  {{ lang.get('auth.password.reset.new') }}
                </label>
                <span class="help-text mbs">
                  {{ lang.get('miscellaneous.password.hint') }}
                </span>
                <div class="vue-field">
                  <validator field-id="password" field-type="password">
                    <password-field v-model="password" name="password" :is-required="true"></password-field>
                    <instant-errors></instant-errors>
                  </validator>
                </div>
              </div>
              <div class="form-group">
                <button
                  id="login"
                  type="submit"
                  class="btn btn-primary btn-lg"
                  :disabled="!buttonEnabled"
                  @click.prevent="submit"
                >
                  {{ lang.get('buttons.save') }}
                </button>
              </div>
            </form>
          </template>
        </simple-widget>
      </div>
    </div>
  </div>
</template>

<script>
import PasswordField from '@/lib/components/Fields/PasswordField.vue';
import SimpleWidget from '@/lib/components/Shared/SimpleWidget.vue';
import langMixin from '@/lib/components/Translations/mixins/lang-mixin';
import AlertIsland from '@/lib/components/Shared/AlertIsland.vue';
import Validator from '@/lib/components/Fields/validator/Validator.vue';
import InstantErrors from '@/lib/components/Fields/validator/InstantErrors.vue';

export default {
  components: {
    AlertIsland,
    PasswordField,
    SimpleWidget,
    Validator,
    InstantErrors
  },
  mixins: [langMixin],
  props: {
    formAction: {
      type: String,
      required: true
    },
    token: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      password: '',
      submitInProgress: false
    };
  },
  computed: {
    buttonEnabled() {
      return !!this.password.length && !this.submitInProgress;
    }
  },
  methods: {
    async submit() {
      this.submitInProgress = true;
      await this.$http
        .post(this.formAction, {
          password: this.password,
          token: this.token
        })
        .catch(() => (this.submitInProgress = false));
    }
  }
};
</script>
