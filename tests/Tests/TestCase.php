<?php

namespace Tests;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Authorization\UserConsumer;
use AwardForce\Library\Cloud\Aws\Adapters\AwsCredentialsAdapter;
use AwardForce\Modules\Accounts\Facades\CurrentAccount;
use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Identity\Roles\Contracts\PermissionRepository;
use AwardForce\Modules\Identity\Roles\Models\Permission;
use AwardForce\Modules\Identity\Roles\Models\PermissionCollection;
use AwardForce\Modules\Identity\Roles\ValueObjects\Mode;
use AwardForce\Modules\Identity\Users\Models\User;
use Carbon\Carbon;
use Illuminate\Contracts\Console\Kernel as ConsoleKernel;
use Illuminate\Foundation\Testing\TestCase as IlluminatedTestCase;
use Illuminate\Translation\Translator;
use Mockery as m;
use Tests\Factory\BackupFacades;
use Tests\Factory\SpeedBurst;
use Tests\Swaps\CurrentAccountService;

/**
 * @deprecated This class should not be extended in tests. Instead, extend `LightUnitTestCase`, `UnitTestCase` or `IntegratedTestCase` depending on your test requirements.
 */
#[\AllowDynamicProperties]
abstract class TestCase extends IlluminatedTestCase
{
    use BackupFacades;
    use HasConsumers;
    use SpeedBurst;

    /**
     * File pointer to the log file.
     */
    protected static $logPointer;

    /**
     * Refresh the application instance.
     *
     * @return void
     */
    protected function refreshApplication()
    {
        parent::refreshApplication();

        $this->mockHasher();
        $this->backupFacades();
        \Mail::fake();
        $this->mockSts();
    }

    /**
     * Creates the application.
     *
     * @return \Symfony\Component\HttpKernel\HttpKernelInterface
     */
    public function createApplication()
    {
        $app = require __DIR__.'/../../bootstrap/app.php';
        $app->make(ConsoleKernel::class)->bootstrap();

        return $app;
    }

    protected function setUp(): void
    {
        $this->log($this->name().' executing...');

        parent::setUp();
        $this->mockConsumer();
        $this->init();
    }

    /**
     * Called by setUp before every test. Good for setting up dependencies and test conditions.
     */
    protected function init()
    {
        // Implement in child classes, instead of setUp
    }

    /**
     * Reset the test case to its base level test status, clearing mocks.
     */
    protected function tearDown(): void
    {
        $this->restoreFacades();

        parent::tearDown();

        // Memory, in MB
        $memoryUsage = round(memory_get_usage() / 1024 / 1024, 3);

        $this->log(Carbon::now()->toDateTimeLocalString().': '.$this->name().' completed using ['.$memoryUsage.'MB] memory.');
    }

    protected function afterTeardown()
    {
    }

    /**
     * Here we setup our own logging function for test logs.
     *
     * @param  string  $message
     */
    protected function log($message)
    {
        if (is_null(static::$logPointer)) {
            static::$logPointer = fopen(__DIR__.'/../../storage/logs/test.log', 'w');
        }

        fwrite(static::$logPointer, $message."\r\n");
    }

    /**
     * Because translation rules are implicit to the system, working with them on each test can be a real
     * pain in the ass (Kirk, Copyright 2015)! So, here we setup a silly mocking solution to get around
     * this rather painful limitation of the translations system that we use.
     */
    protected function mockTranslations()
    {
        $this->translationLoader = m::mock(LoaderInterface::class);
        $this->translationLoader->shouldReceive('load');

        $this->translator = m::mock(Translator::class);
        $this->translator->shouldReceive('getLoader')->andReturn($this->translationLoader);
        $this->translator->shouldReceive('locale')->andReturn('en_GB');

        $this->translator->shouldReceive('get')->andReturnUsing(function ($key) {
            return $key;
        })->byDefault();

        $this->app->instance('translator', $this->translator);
    }

    protected function parallelToken()
    {
        return getenv('TEST_TOKEN');
    }

    public function beginDatabaseTransaction()
    {
        $database = $this->app->make('db');

        foreach ($this->connectionsToTransact() as $name) {
            $connection = $database->connection($name);
            try {
                $connection->rollback();
            } catch (\PDOException $e) {
                // In case there is no active transaction
            }
            $dispatcher = $connection->getEventDispatcher();

            $connection->unsetEventDispatcher();
            $connection->beginTransaction();
            $connection->setEventDispatcher($dispatcher);
        }

        $this->beforeApplicationDestroyed(function () use ($database) {
            foreach ($this->connectionsToTransact() as $name) {
                $connection = $database->connection($name);
                $dispatcher = $connection->getEventDispatcher();

                $connection->unsetEventDispatcher();
                try {
                    $connection->rollback();
                } catch (\PDOException $e) {
                    // In case there is no active transaction
                }
                $connection->setEventDispatcher($dispatcher);
                $connection->disconnect();

                if (! $this->parallelToken()) {
                    $this->app->make('cache')->flush();
                }
            }
        });
    }

    private function mockSts()
    {
        $stsAdapter = m::mock(AwsCredentialsAdapter::class, [$this->app['config']]);
        $stsAdapter->shouldReceive('key')->andReturn('temp_key');
        $stsAdapter->shouldReceive('secret')->andReturn('temp_secret');
        $stsAdapter->shouldReceive('token')->andReturn('temp_token');
        $this->app->instance(AwsCredentialsAdapter::class, $stsAdapter);
    }

    public function assertContainsInstanceOf(string $expected, iterable $haystack, ?string $message = null): void
    {
        $found = false;

        foreach ($haystack as $item) {
            if ($item instanceof $expected) {
                $found = true;
                break;
            }
        }

        $this->assertTrue($found, $message ?? "Expected to find an instance of $expected");
    }

    public function assertNotContainsInstanceOf(string $expected, iterable $haystack, ?string $message = null): void
    {
        $found = false;

        foreach ($haystack as $item) {
            if ($item instanceof $expected) {
                $found = true;
                break;
            }
        }

        $this->assertFalse($found, $message ?? "Expected not to find an instance of $expected");
    }

    public function setupAccount()
    {
        $account = new Account;
        $account->id = Account::max('id') + 1;
        $account->save();
        $account->languages()->delete();

        CurrentAccount::swap(new CurrentAccountService($account));
    }

    /**
     * This function is used to mock permissions for a user in the system.
     * It creates a mock of the PermissionRepository and sets it to return a specific permission when queried.
     * This is useful in tests where you need to simulate a user having certain permissions.
     *
     * @param  User  $user  The user for whom the permissions are being mocked.
     * @param  string  $resource  The resource that the permission applies to.
     * @param  string  $action  The action that the permission allows or denies.
     * @param  string  $mode  The mode of the permission, either 'allow' or 'deny'.
     * @return PermissionRepository The mocked PermissionRepository.
     */
    public function mockPermissions(User $user, string $resource, string $action, string $mode = 'allow'): PermissionRepository
    {
        $permissions = m::mock(PermissionRepository::class);
        $permissions
            ->shouldReceive('getByAccountAndUserId')
            ->with(current_account()->id, $user->id)
            ->andReturn(new PermissionCollection([
                new Permission(['resource' => $resource, 'action' => $action, 'mode' => new Mode($mode)]),
            ]));
        $this->app->instance(PermissionRepository::class, $permissions);
        Consumer::set(new UserConsumer($user, $permissions));

        return $permissions;
    }
}
