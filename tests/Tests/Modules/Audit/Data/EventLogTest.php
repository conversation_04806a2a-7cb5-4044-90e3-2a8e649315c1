<?php

namespace Tests\Modules\Audit\Data;

use AwardForce\Modules\Audit\Data\EventLog;
use AwardForce\Modules\Audit\Events\SystemResource;
use Eloquence\Behaviours\Slug;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

final class EventLogTest extends BaseTestCase
{
    use Laravel;

    public function init()
    {
        SystemResource::setResources(['auth']);
    }

    public function testItCanHandleDataInput(): void
    {
        $log = new EventLog;
        $log->data = ['something' => 'value'];

        $this->assertSame(['something' => 'value'], $log->data);
    }

    public function testItCanManageSystemResourceObjects(): void
    {
        $log = new EventLog;
        $log->resource = new SystemResource('auth');

        $this->assertSame('auth', (string) $log->resource);
    }

    public function testDescriptionDataOnlyReturnsValidReplacements(): void
    {
        $log = new EventLog;
        $log->data = [
            'numeric' => 123,
            'string' => 'replacement',
            'invalid' => ['array'],
        ];

        $this->assertEquals([
            'numeric' => 123,
            'string' => 'replacement',
        ], $log->descriptionData());
    }

    public function testItDoesNotAllowDoubleCurlyBracesInData()
    {
        $log = new EventLog;
        $log->data = [
            'string' => '{{replacement}}',
        ];

        $this->assertEquals([
            'string' => '{ {replacement} }',
        ], $log->descriptionData());
    }

    public function testItMergesSlugValueIfItIsNotAlreadyThereInTheData(): void
    {
        $log = new EventLog;
        $log->data = [
            'string' => 'yoda',
            'slug' => [],
        ];
        $log->slug = $slug = (string) Slug::random();

        $this->assertEquals([
            'string' => 'yoda',
            'slug' => $slug,
        ], $log->descriptionData());
    }
}
