<?php

namespace Tests\Modules\Audit\Data;

use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Audit\Data\EventLog;
use AwardForce\Modules\Audit\Data\EventLogRepository;
use AwardForce\Modules\Identity\Users\Models\User;
use Tests\ElasticSearchTestCase;
use Tests\Support\InteractsWithEventLogs;

final class EventLogRepositoryTest extends ElasticSearchTestCase
{
    use InteractsWithEventLogs;

    private EventLogRepository $eventLogs;

    public function init()
    {
        $this->eventLogs = $this->app->make(EventLogRepository::class);
    }

    public function testItReturnsEventLogsOfTheRequiredUser(): void
    {
        $this->muffin(EventLog::class, ['user_id' => ($user = $this->muffin(User::class))->id]);
        $this->muffin(EventLog::class, ['user_id' => $user->id]);
        $this->muffin(EventLog::class, ['user_id' => $this->muffin(User::class)->id]);

        $this->refreshEventLogsIndex();
        $logs = $this->eventLogs->forUser($user->id);

        $this->assertSame(2, $logs->count());
    }

    public function testDeleteUserLogs(): void
    {
        // User event logs on non user resources
        $this->muffins(2, EventLog::class, [
            'user_id' => ($user = $this->muffin(User::class))->id,
            'resource' => 'entry',
        ]);

        // Event logs for user resource - to be deleted
        $this->muffins(5, EventLog::class, [
            'user_id' => $this->muffin(User::class)->id,
            'resource' => 'user',
            'foreign_id' => $user->id,
        ]);

        // Event logs for user resource for different account - not to be deleted
        $this->muffins(3, EventLog::class, [
            'user_id' => $this->muffin(User::class)->id,
            'account_id' => ($otherAccount = $this->muffin(Account::class))->id,
            'resource' => 'user',
            'foreign_id' => $user->id,
        ]);
        $this->refreshEventLogsIndex();

        $this->assertCount(5, $this->eventLogs->forResource('user', $user->id));

        $this->assertEquals(5, $this->eventLogs->deleteForUser($user->id));

        $this->refreshEventLogsIndex();

        $this->assertCount(0, $this->eventLogs->forResource('user', $user->id));

        \CurrentAccount::set($otherAccount);

        $this->assertEquals(3, $this->eventLogs->deleteForUser($user->id));

        $this->refreshEventLogsIndex();

        $this->assertCount(0, $this->eventLogs->forResource('user', $user->id));
    }
}
