<?php

namespace Tests\Support;

use AwardForce\Modules\Audit\Data\EventLog;

trait InteractsWithEventLogs
{
    protected function deleteExistingEventLogs()
    {
        $this->refreshEventLogsIndex();
        $parameters['index'] = (new EventLog)->getIndex();
        $parameters['type'] = (new EventLog)->getType();
        $parameters['body'] = [
            'query' => [
                'match_all' => (object) [],
            ],
        ];
        EventLog::raw()->deleteByQuery($parameters);
        $this->refreshEventLogsIndex();
    }

    protected function refreshEventLogsIndex()
    {
        EventLog::refreshIndex();
    }
}
